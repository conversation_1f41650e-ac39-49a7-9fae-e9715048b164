<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>{{ if .IsHome }}{{ site.Title }}{{ else }}{{ printf "%s | %s" .Title site.Title }}{{ end }}</title>
<meta name="description" content="{{ .Site.Params.description }}">
<meta name="author" content="{{ .Site.Params.author }}">

<!-- Cache busting for development -->
<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Expires" content="0">

<!-- Favicon -->
<link rel="icon" type="image/x-icon" href="{{ "favicon.ico" | relURL }}">

<!-- CSS -->
<link rel="stylesheet" href="{{ "css/main.css" | relURL }}">
<link rel="stylesheet" href="{{ "css/auth-ui.css" | relURL }}">
