<!-- Logo Section -->
<div class="header-logo">
  <a href="#" class="logo">
    <div class="logo-icon">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
        <path d="M8 5v14l11-7z"/>
      </svg>
    </div>
    Tubli
  </a>
  <div class="header-current-playing">
    <button id="header-prev-btn" class="header-control-btn" disabled>
      <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
        <path d="M6 6h2v12H6zm3.5 6l8.5 6V6z"/>
      </svg>
    </button>
    <div class="header-playing-info">
      <span id="header-current-video-info">Ready to play</span>
    </div>
    <button id="header-next-btn" class="header-control-btn" disabled>
      <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
        <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z"/>
      </svg>
    </button>
  </div>

  <!-- Authentication Section -->
  <div class="auth-container">
    <div id="auth-status-indicator" class="auth-status-indicator not-authenticated" title="Not signed in"></div>

    <div id="auth-sign-in-container" class="auth-sign-in-container" style="display: flex;">
      <button id="auth-google-sign-in-btn" class="auth-google-sign-in-btn">
        <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
          <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
          <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
          <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
          <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
        </svg>
        Continue with Google
      </button>

      <button id="auth-email-sign-in-btn" class="auth-email-sign-in-btn">
        <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
          <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
        </svg>
        Continue with Email
      </button>
    </div>

    <button id="auth-user-profile-btn" class="auth-user-profile-btn" style="display: none;">
      <!-- User profile content will be populated by JavaScript -->
    </button>

    <div id="auth-user-dropdown" class="auth-user-dropdown">
      <!-- Dropdown content will be populated by JavaScript -->
    </div>
  </div>

  <button class="header-minimize-toggle" id="header-minimize-toggle" title="Minimize Media Player">
    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
      <path d="M19 13H5v-2h14v2z"/>
    </svg>
  </button>
</div>

<!-- Media Control Center -->
<div class="media-control-center">
  <!-- Video Player Section -->
  <div id="player"></div>


  <!-- Current Queue Section -->
  <div class="header-current-queue">
    <h3 class="queue-title">
      <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
        <path d="M15 6H3v2h12V6zm0 4H3v2h12v-2zM3 16h8v-2H3v2zM17 6v8.18c-.31-.11-.65-.18-1-.18-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3V8h3V6h-5z"/>
      </svg>
      Queue
      <span id="queue-count" class="queue-count">0</span>
    </h3>
    <div class="queue-actions">
      <button id="play-btn" class="queue-action-btn queue-action-play" title="Play Queue">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M8 5v14l11-7z"/>
        </svg>
      </button>
      <button id="clear-queue-btn" class="queue-action-btn queue-action-clear" title="Clear Queue">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
        </svg>
      </button>
    </div>
    <div id="queue-container" class="queue-container">
      <div class="queue-empty">
        <div class="queue-empty-icon">♪</div>
        <p>Your queue is empty</p>
        <p>Add videos to start playing!</p>
      </div>
    </div>
  </div>
</div>
