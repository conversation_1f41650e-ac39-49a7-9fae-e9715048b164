// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Check if a text string is a valid YouTube URL
 * @param {string} text - The text to check
 * @returns {boolean} - True if it's a YouTube URL
 */
function isYouTubeURL(text) {
  const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|youtube\.com\/v\/)/;
  return youtubeRegex.test(text.trim());
}

/**
 * Extract video ID from YouTube URL
 * @param {string} url - YouTube URL
 * @returns {string|null} - Video ID or null if invalid
 */
function extractVideoId(url) {
  const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
  const match = url.match(regExp);
  return (match && match[2].length === 11) ? match[2] : null;
}

/**
 * Format duration from seconds to MM:SS or HH:MM:SS
 * @param {number} seconds - Duration in seconds
 * @returns {string} - Formatted duration
 */
function formatDuration(seconds) {
  if (!seconds || seconds === 0) return '0:00';

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
}

/**
 * Format duration from ISO 8601 format (PT4M13S) to readable format
 * @param {string} duration - ISO 8601 duration string
 * @returns {string} - Formatted duration
 */
function formatDurationFromISO8601(duration) {
  if (!duration || duration === 'PT0S') return '0:00';

  const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
  if (!match) return 'Unknown';

  const hours = parseInt(match[1] || 0);
  const minutes = parseInt(match[2] || 0);
  const seconds = parseInt(match[3] || 0);

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
}

/**
 * Format view count for display
 * @param {number} viewCount - Number of views
 * @returns {string} - Formatted view count
 */
function formatViews(viewCount) {
  if (!viewCount || viewCount === 0) return 'No views';

  if (viewCount >= 1000000000) {
    return (viewCount / 1000000000).toFixed(1) + 'B views';
  } else if (viewCount >= 1000000) {
    return (viewCount / 1000000).toFixed(1) + 'M views';
  } else if (viewCount >= 1000) {
    return (viewCount / 1000).toFixed(1) + 'K views';
  } else {
    return viewCount + ' views';
  }
}

/**
 * Format published date to relative time
 * @param {string} publishedAt - ISO date string
 * @returns {string} - Formatted relative date
 */
function formatPublishedDate(publishedAt) {
  if (!publishedAt) return 'Unknown';

  const publishedDate = new Date(publishedAt);
  const now = new Date();
  const diffTime = Math.abs(now - publishedDate);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays < 1) {
    return 'Today';
  } else if (diffDays < 7) {
    return `${diffDays} days ago`;
  } else if (diffDays < 30) {
    const weeks = Math.floor(diffDays / 7);
    return `${weeks} week${weeks > 1 ? 's' : ''} ago`;
  } else if (diffDays < 365) {
    const months = Math.floor(diffDays / 30);
    return `${months} month${months > 1 ? 's' : ''} ago`;
  } else {
    const years = Math.floor(diffDays / 365);
    return `${years} year${years > 1 ? 's' : ''} ago`;
  }
}

/**
 * Escape HTML to prevent XSS attacks
 * @param {string} text - Text to escape
 * @returns {string} - Escaped HTML
 */
function escapeHtml(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

/**
 * Decode HTML entities to display text properly
 * @param {string} text - Text with HTML entities to decode
 * @returns {string} - Decoded text
 */
function decodeHtmlEntities(text) {
  const div = document.createElement('div');
  div.innerHTML = text;
  return div.textContent || div.innerText || '';
}

/**
 * Generate a unique queue ID
 * @returns {string} - Unique queue ID
 */
function generateQueueId() {
  return 'queue_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

/**
 * Get user ID from Firebase Auth for personal queue storage
 * @returns {Promise<string|null>} - User ID or null
 */
async function getSessionId() {
  if (typeof getUserId === 'function') {
    return await getUserId();
  }

  console.error('Firebase Auth not available - getUserId function not found');
  return null;
}

// extractQueueMetadata function moved to firebase-queue-metadata.js

/**
 * Debounce function to limit function calls
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} - Debounced function
 */
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

console.log('✅ Utils module loaded');
