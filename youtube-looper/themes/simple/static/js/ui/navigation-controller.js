// ============================================================================
// NAVIGATION CONTROLLER
// ============================================================================

/**
 * Navigation Controller
 * Handles view switching, navigation state management, and content loading
 * Manages the main application navigation between search, personal, and public views
 */

/**
 * Current active view
 */
let currentView = 'search';

/**
 * Initialize navigation functionality
 */
function initializeNavigation() {
  console.log('🧭 Initializing navigation...');
  
  // Add event listeners to navigation items
  const navItems = document.querySelectorAll('.nav-item');
  navItems.forEach(item => {
    item.addEventListener('click', function() {
      const view = this.getAttribute('data-view');
      switchToView(view);
    });
  });

  // Initialize with search view
  switchToView('search');
  
  console.log('✅ Navigation initialized');
}

/**
 * Switch to a specific view
 * @param {string} viewName - Name of the view to switch to
 */
function switchToView(viewName) {
  console.log(`🧭 Switching to view: ${viewName}`);

  // Update navigation active state
  const navItems = document.querySelectorAll('.nav-item');
  navItems.forEach(item => {
    if (item.getAttribute('data-view') === viewName) {
      item.classList.add('active');
    } else {
      item.classList.remove('active');
    }
  });

  // Update view content visibility
  const viewContents = document.querySelectorAll('.view-content');
  viewContents.forEach(view => {
    view.classList.remove('active');
  });

  const targetView = document.getElementById(`${viewName}-view`);
  if (targetView) {
    targetView.classList.add('active');
  }

  // Header minimize functionality is now independent of view switching
  // The header media controls are always available regardless of current view

  // Update current view
  currentView = viewName;

  // Load content for the view
  loadViewContent(viewName);
}

/**
 * Load content for a specific view
 * @param {string} viewName - Name of the view to load content for
 */
function loadViewContent(viewName) {
  switch (viewName) {
    case 'search':
      // Search view is always ready
      break;
    case 'personal':
      if (typeof loadPersonalQueues === 'function') {
        loadPersonalQueues();
      }
      break;
    case 'public':
      if (typeof loadPublicQueues === 'function') {
        loadPublicQueues();
      }
      break;
  }
}

/**
 * Get current active view
 * @returns {string} - Current view name
 */
function getCurrentView() {
  return currentView;
}

/**
 * Load public queues (integrates with existing queue browser)
 */
async function loadPublicQueues() {
  const container = document.getElementById('public-queues-list');
  if (!container) return;

  // Show loading state
  container.innerHTML = `
    <div class="queues-loading">
      <div class="loading-spinner"></div>
      <p>Discovering amazing queues from the community...</p>
    </div>
  `;

  try {
    // Get sort option
    const sortSelect = document.getElementById('sort-select');
    const sortBy = sortSelect ? sortSelect.value : 'recent';

    // Load queues from Firebase
    const result = await listQueuesFromFirebase(sortBy, 20);

    if (result.success && result.data.queues.length > 0) {
      displayPublicQueues(result.data.queues);
    } else {
      container.innerHTML = `
        <div class="queues-empty">
          <div class="empty-icon">🌟</div>
          <h3>No public queues yet</h3>
          <p>Be the first to share an amazing queue with the community! Create a queue and make it public to get started.</p>
        </div>
      `;
    }
  } catch (error) {
    console.error('Error loading public queues:', error);
    container.innerHTML = `
      <div class="queues-error">
        <div class="error-icon">⚠️</div>
        <h3>Unable to load public queues</h3>
        <p>We're having trouble connecting to our servers. Please check your internet connection and try again.</p>
        <button onclick="loadPublicQueues()" class="retry-btn">Try Again</button>
      </div>
    `;
  }
}

/**
 * Display public queues in the list
 * @param {Array} queues - Array of queue objects
 */
function displayPublicQueues(queues) {
  const container = document.getElementById('public-queues-list');
  if (!container) return;

  const queueHTML = queues.map(queue => `
    <div class="queue-card public-queue" data-queue-id="${queue.queueId}">
      <div class="queue-thumbnail">
        ${queue.thumbnail ?
          `<img src="${queue.thumbnail}" alt="${queue.title}" />` :
          '<div class="placeholder-thumbnail">🎵</div>'
        }
        <div class="queue-overlay">
          <button class="play-queue-btn" onclick="loadAndPlayQueue('${queue.queueId}')">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M8 5v14l11-7z"/>
            </svg>
          </button>
        </div>
      </div>
      <div class="queue-info">
        <h3 class="queue-title">${queue.title}</h3>
        <div class="queue-meta">
          <span class="video-count">${queue.videoCount} videos</span>
          <span class="duration">${formatDuration(queue.totalDuration)}</span>
          <span class="views">${queue.viewCount} plays</span>
        </div>
        <div class="queue-date">
          ${formatRelativeTime(queue.lastModified)}
        </div>
      </div>
    </div>
  `).join('');

  container.innerHTML = queueHTML;

  // Add event listeners for public queue card clicks
  initializePublicQueueCardClickHandlers();
}

/**
 * Initialize click handlers for public queue cards to make entire card clickable
 */
function initializePublicQueueCardClickHandlers() {
  const queueCards = document.querySelectorAll('.queue-card.public-queue');

  queueCards.forEach(card => {
    card.addEventListener('click', function(event) {
      // Don't trigger queue loading if clicking on interactive elements
      const clickedElement = event.target;
      const isInteractiveElement = clickedElement.closest('.play-queue-btn') ||
                                   clickedElement.closest('button');

      if (isInteractiveElement) {
        return; // Let the specific element handle its own click
      }

      // Get queue ID and load the queue
      const queueId = this.dataset.queueId;
      if (queueId) {
        loadAndPlayQueue(queueId);
      }
    });
  });
}

/**
 * Load and play a queue from the public list
 * @param {string} queueId - ID of the queue to load
 */
async function loadAndPlayQueue(queueId) {
  try {
    console.log('Loading queue...');

    // Load the queue
    const success = await loadQueueFromFirebase(queueId);

    if (success) {
      console.log('Queue loaded successfully');

      // Auto-play if there are videos
      const videoQueue = getVideoQueue();
      if (videoQueue.length > 0) {
        setTimeout(() => {
          playQueue();
        }, 1000);
      }
    }
  } catch (error) {
    console.error('Error loading queue:', error);
    console.log('Failed to load queue:', error);
  }
}

/**
 * Initialize sort functionality for public queues
 */
function initializePublicQueueSort() {
  const sortSelect = document.getElementById('sort-select');
  if (sortSelect) {
    sortSelect.addEventListener('change', function() {
      if (getCurrentView() === 'public') {
        loadPublicQueues();
      }
    });
  }
}

/**
 * Toggle header minimize mode with enhanced animations
 */
function toggleHeaderMinimize() {
  const header = document.querySelector('header');
  const toggleBtn = document.getElementById('header-minimize-toggle');
  const mediaControlCenter = header.querySelector('.media-control-center');
  const isCurrentlyMinimized = header.classList.contains('minimized');

  // Add animation class to button
  if (toggleBtn) {
    toggleBtn.classList.add('animating');
    setTimeout(() => {
      toggleBtn.classList.remove('animating');
    }, 600);
  }

  if (isCurrentlyMinimized) {
    // Expanding
    header.classList.remove('minimized');

    // Trigger expand animation
    if (mediaControlCenter) {
      mediaControlCenter.style.animation = 'headerExpand 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards';
    }

    console.log('🎵 Header media controls expanded');
    localStorage.setItem('headerMinimized', 'false');
  } else {
    // Minimizing
    if (mediaControlCenter) {
      mediaControlCenter.style.animation = 'headerCollapse 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards';
    }

    // Add minimized class after a short delay to allow animation to start
    setTimeout(() => {
      header.classList.add('minimized');
    }, 50);

    console.log('🎵 Header media controls minimized');
    localStorage.setItem('headerMinimized', 'true');
  }

  // Clear animation after completion
  setTimeout(() => {
    if (mediaControlCenter) {
      mediaControlCenter.style.animation = '';
    }
  }, 500);
}

/**
 * Check if header should be minimized based on user preference
 */
function shouldMinimizeHeader() {
  return localStorage.getItem('headerMinimized') === 'true';
}

/**
 * Initialize header minimize toggle functionality
 */
function initializeHeaderMinimizeToggle() {
  const toggleBtn = document.getElementById('header-minimize-toggle');
  if (toggleBtn) {
    toggleBtn.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      toggleHeaderMinimize();
    });
  }

  // Apply saved preference on load
  if (shouldMinimizeHeader()) {
    const header = document.querySelector('header');
    if (header) {
      header.classList.add('minimized');
    }
  }
}

// Initialize functionality when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
  initializePublicQueueSort();
  initializeHeaderMinimizeToggle();
});
