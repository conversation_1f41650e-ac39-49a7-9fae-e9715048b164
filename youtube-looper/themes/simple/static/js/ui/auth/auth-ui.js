// ============================================================================
// AUTHENTICATION UI CONTROLLER
// ============================================================================

/**
 * Authentication UI state
 */
let authUIState = {
  isInitialized: false,
  currentUser: null,
  isAuthenticated: false,
  showingEmailForm: false,
  isSignUp: false
};

/**
 * Initialize authentication UI
 */
function initializeAuthUI() {
  console.log('🔐 Initializing authentication UI...');
  
  // Set up event listeners
  setupAuthEventListeners();
  
  // Set up global auth state listener
  document.addEventListener('authStateChanged', handleAuthStateChange);
  
  // Initialize UI state
  updateAuthUI();
  
  authUIState.isInitialized = true;
  console.log('✅ Authentication UI initialized');
}

/**
 * Set up authentication event listeners
 */
function setupAuthEventListeners() {
  // Google sign in button
  const googleSignInBtn = document.getElementById('auth-google-sign-in-btn');
  if (googleSignInBtn) {
    googleSignInBtn.addEventListener('click', handleGoogleSignIn);
  }

  // Email sign in button
  const emailSignInBtn = document.getElementById('auth-email-sign-in-btn');
  if (emailSignInBtn) {
    emailSignInBtn.addEventListener('click', showEmailForm);
  }

  // Sign out button
  const signOutBtn = document.getElementById('auth-sign-out-btn');
  if (signOutBtn) {
    signOutBtn.addEventListener('click', handleSignOut);
  }

  // User profile dropdown toggle
  const userProfileBtn = document.getElementById('auth-user-profile-btn');
  if (userProfileBtn) {
    userProfileBtn.addEventListener('click', toggleUserProfileDropdown);
  }

  // Close dropdown when clicking outside
  document.addEventListener('click', (event) => {
    const dropdown = document.getElementById('auth-user-dropdown');
    const profileBtn = document.getElementById('auth-user-profile-btn');
    const authModal = document.getElementById('auth-modal');

    if (dropdown && profileBtn &&
        !dropdown.contains(event.target) &&
        !profileBtn.contains(event.target)) {
      dropdown.classList.remove('show');
    }

    // Close auth modal when clicking outside
    if (authModal && event.target === authModal) {
      hideEmailForm();
    }
  });

  // Handle window resize and scroll to reposition dropdown if open
  function repositionDropdown() {
    const dropdown = document.getElementById('auth-user-dropdown');
    const profileBtn = document.getElementById('auth-user-profile-btn');

    if (dropdown && profileBtn && dropdown.classList.contains('show')) {
      const rect = profileBtn.getBoundingClientRect();
      dropdown.style.top = `${rect.bottom + 8}px`;
      dropdown.style.right = `${window.innerWidth - rect.right}px`;
    }
  }

  window.addEventListener('resize', repositionDropdown);
  window.addEventListener('scroll', repositionDropdown);


}

/**
 * Handle authentication state changes
 * @param {CustomEvent} event - Auth state change event
 */
function handleAuthStateChange(event) {
  const { isAuthenticated, user } = event.detail || {};
  
  authUIState.isAuthenticated = isAuthenticated;
  authUIState.currentUser = user;
  
  console.log('🔐 Auth UI state changed:', isAuthenticated, user?.uid);
  
  updateAuthUI();
}

/**
 * Handle Google sign in button click
 */
async function handleGoogleSignIn() {
  console.log('🔐 Google sign in requested');

  const signInBtn = document.getElementById('auth-google-sign-in-btn');
  if (signInBtn) {
    signInBtn.disabled = true;
    signInBtn.innerHTML = `
      <div class="auth-loading">
        <div class="auth-spinner"></div>
        Signing in...
      </div>
    `;
  }

  try {
    if (typeof signInWithGoogle === 'function') {
      const user = await signInWithGoogle();
      if (user) {
        console.log('✅ Google sign in successful');
        showAuthNotification('Welcome! You are now signed in.', 'success');
      } else {
        console.log('❌ Google sign in cancelled or failed');
        showAuthNotification('Sign in was cancelled or failed. Please try again.', 'error');
      }
    } else {
      console.error('signInWithGoogle function not available');
      showAuthNotification('Authentication service not available.', 'error');
    }
  } catch (error) {
    console.error('❌ Google sign in error:', error);
    showAuthNotification('Sign in failed. Please try again.', 'error');
  } finally {
    // Reset button state
    if (signInBtn) {
      signInBtn.disabled = false;
      updateGoogleSignInButton();
    }
  }
}

/**
 * Show email authentication form
 */
function showEmailForm() {
  authUIState.showingEmailForm = true;
  authUIState.isSignUp = false;

  // Create modal if it doesn't exist
  let modal = document.getElementById('auth-modal');
  if (!modal) {
    modal = document.createElement('div');
    modal.id = 'auth-modal';
    modal.className = 'auth-modal';
    document.body.appendChild(modal);
  }

  updateEmailForm();
  modal.classList.add('show');
}

/**
 * Hide email authentication form
 */
function hideEmailForm() {
  authUIState.showingEmailForm = false;

  const modal = document.getElementById('auth-modal');
  if (modal) {
    modal.classList.remove('show');
  }
}

/**
 * Toggle between sign in and sign up forms
 */
function toggleSignUpMode() {
  authUIState.isSignUp = !authUIState.isSignUp;
  updateEmailForm();
}

/**
 * Handle email form submission
 */
async function handleEmailFormSubmit(event) {
  event.preventDefault();

  const form = event.target;
  const email = form.email.value.trim();
  const password = form.password.value;
  const displayName = form.displayName ? form.displayName.value.trim() : null;

  if (!email || !password) {
    showAuthNotification('Please fill in all required fields.', 'error');
    return;
  }

  const submitBtn = form.querySelector('.auth-form-submit');
  const originalText = submitBtn.innerHTML;

  submitBtn.disabled = true;
  submitBtn.innerHTML = `
    <div class="auth-loading">
      <div class="auth-spinner"></div>
      ${authUIState.isSignUp ? 'Creating account...' : 'Signing in...'}
    </div>
  `;

  try {
    let user;
    if (authUIState.isSignUp) {
      if (typeof createAccountWithEmail === 'function') {
        user = await createAccountWithEmail(email, password, displayName);
        showAuthNotification('Account created successfully! Welcome!', 'success');
      } else {
        throw new Error('Account creation not available');
      }
    } else {
      if (typeof signInWithEmail === 'function') {
        user = await signInWithEmail(email, password);
        showAuthNotification('Welcome back! You are now signed in.', 'success');
      } else {
        throw new Error('Email sign in not available');
      }
    }

    if (user) {
      hideEmailForm();
    }
  } catch (error) {
    console.error('❌ Email auth error:', error);

    // Handle specific Firebase error codes
    let errorMessage = 'Authentication failed. Please try again.';
    if (error.code) {
      switch (error.code) {
        case 'auth/email-already-in-use':
          errorMessage = 'An account with this email already exists.';
          break;
        case 'auth/invalid-email':
          errorMessage = 'Please enter a valid email address.';
          break;
        case 'auth/weak-password':
          errorMessage = 'Password should be at least 6 characters.';
          break;
        case 'auth/user-not-found':
          errorMessage = 'No account found with this email.';
          break;
        case 'auth/wrong-password':
          errorMessage = 'Incorrect password.';
          break;
        case 'auth/too-many-requests':
          errorMessage = 'Too many failed attempts. Please try again later.';
          break;
      }
    }

    showAuthNotification(errorMessage, 'error');
  } finally {
    submitBtn.disabled = false;
    submitBtn.innerHTML = originalText;
  }
}

/**
 * Handle forgot password
 */
async function handleForgotPassword() {
  const emailInput = document.getElementById('auth-email-input');
  const email = emailInput ? emailInput.value.trim() : '';

  if (!email) {
    showAuthNotification('Please enter your email address first.', 'error');
    return;
  }

  try {
    if (typeof sendPasswordResetEmail === 'function') {
      await sendPasswordResetEmail(email);
      showAuthNotification('Password reset email sent! Check your inbox.', 'success');
    } else {
      throw new Error('Password reset not available');
    }
  } catch (error) {
    console.error('❌ Password reset error:', error);

    let errorMessage = 'Failed to send password reset email.';
    if (error.code === 'auth/user-not-found') {
      errorMessage = 'No account found with this email address.';
    } else if (error.code === 'auth/invalid-email') {
      errorMessage = 'Please enter a valid email address.';
    }

    showAuthNotification(errorMessage, 'error');
  }
}

/**
 * Handle sign out button click
 */
async function handleSignOut() {
  console.log('🔓 Sign out requested');
  
  try {
    if (typeof signOut === 'function') {
      const success = await signOut();
      if (success) {
        console.log('✅ Sign out successful');
        showAuthNotification('You have been signed out.', 'info');
      } else {
        console.log('❌ Sign out failed');
        showAuthNotification('Sign out failed. Please try again.', 'error');
      }
    } else {
      console.error('signOut function not available');
    }
  } catch (error) {
    console.error('❌ Sign out error:', error);
    showAuthNotification('Sign out failed. Please try again.', 'error');
  }
}

/**
 * Toggle user profile dropdown
 */
function toggleUserProfileDropdown() {
  // Don't show dropdown for anonymous users
  if (authUIState.currentUser && authUIState.currentUser.isAnonymous) {
    return;
  }

  const dropdown = document.getElementById('auth-user-dropdown');
  const profileBtn = document.getElementById('auth-user-profile-btn');

  if (dropdown && profileBtn) {
    if (dropdown.classList.contains('show')) {
      dropdown.classList.remove('show');
    } else {
      // Move dropdown to body to escape stacking context issues
      if (dropdown.parentElement !== document.body) {
        document.body.appendChild(dropdown);
      }

      // Position the dropdown relative to the profile button
      const rect = profileBtn.getBoundingClientRect();
      dropdown.style.position = 'fixed';
      dropdown.style.top = `${rect.bottom + 8}px`;
      dropdown.style.right = `${window.innerWidth - rect.right}px`;
      dropdown.style.left = 'auto';
      dropdown.classList.add('show');
    }
  }
}

/**
 * Update authentication UI based on current state
 */
function updateAuthUI() {
  updateSignInButtons();
  updateUserProfile();
  updateAuthStatus();
  if (authUIState.showingEmailForm) {
    updateEmailForm();
  }
}

/**
 * Update sign in buttons
 */
function updateSignInButtons() {
  const authContainer = document.getElementById('auth-sign-in-container');
  if (!authContainer) return;

  if (authUIState.isAuthenticated) {
    authContainer.style.display = 'none';
  } else {
    authContainer.style.display = 'flex';
    updateGoogleSignInButton();
    updateEmailSignInButton();
  }
}

/**
 * Update Google sign in button
 */
function updateGoogleSignInButton() {
  const googleSignInBtn = document.getElementById('auth-google-sign-in-btn');
  if (!googleSignInBtn) return;

  googleSignInBtn.innerHTML = `
    <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
      <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
      <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
      <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
      <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
    </svg>
    Continue with Google
  `;
}

/**
 * Update email sign in button
 */
function updateEmailSignInButton() {
  const emailSignInBtn = document.getElementById('auth-email-sign-in-btn');
  if (!emailSignInBtn) return;

  emailSignInBtn.innerHTML = `
    <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
      <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
    </svg>
    Continue with Email
  `;
}

/**
 * Update email form content
 */
function updateEmailForm() {
  const modal = document.getElementById('auth-modal');
  if (!modal) return;

  const isSignUp = authUIState.isSignUp;

  modal.innerHTML = `
    <div class="auth-modal-content">
      <div class="auth-modal-header">
        <h2>${isSignUp ? 'Create Account' : 'Sign In'}</h2>
        <button class="auth-modal-close" onclick="hideEmailForm()">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
          </svg>
        </button>
      </div>

      <form class="auth-form" onsubmit="handleEmailFormSubmit(event)">
        ${isSignUp ? `
          <div class="auth-form-group">
            <label for="auth-name-input">Display Name</label>
            <input type="text" id="auth-name-input" name="displayName" placeholder="Your name" />
          </div>
        ` : ''}

        <div class="auth-form-group">
          <label for="auth-email-input">Email</label>
          <input type="email" id="auth-email-input" name="email" placeholder="<EMAIL>" required />
        </div>

        <div class="auth-form-group">
          <label for="auth-password-input">Password</label>
          <input type="password" id="auth-password-input" name="password" placeholder="Password" required />
        </div>

        <button type="submit" class="auth-form-submit">
          ${isSignUp ? 'Create Account' : 'Sign In'}
        </button>

        ${!isSignUp ? `
          <button type="button" class="auth-form-forgot" onclick="handleForgotPassword()">
            Forgot Password?
          </button>
        ` : ''}
      </form>

      <div class="auth-form-footer">
        <p>
          ${isSignUp ? 'Already have an account?' : "Don't have an account?"}
          <button class="auth-form-toggle" onclick="toggleSignUpMode()">
            ${isSignUp ? 'Sign In' : 'Sign Up'}
          </button>
        </p>
      </div>
    </div>
  `;
}

/**
 * Update user profile display
 */
function updateUserProfile() {
  const userProfileBtn = document.getElementById('auth-user-profile-btn');
  const userDropdown = document.getElementById('auth-user-dropdown');
  
  if (!userProfileBtn) return;
  
  if (authUIState.isAuthenticated && authUIState.currentUser) {
    const user = authUIState.currentUser;
    userProfileBtn.style.display = 'flex';
    
    // Update profile button
    const displayName = user.isAnonymous ? 'Anonymous' : (user.displayName || 'User');
    const avatarLetter = user.isAnonymous ? 'A' : (user.displayName || user.email || 'U').charAt(0).toUpperCase();

    if (user.isAnonymous) {
      // For anonymous users, show only an icon
      userProfileBtn.innerHTML = `
        <div class="auth-anonymous-icon" title="Anonymous mode - Sign in with Google to save your queues permanently">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
          </svg>
        </div>
      `;
    } else {
      // For authenticated users, show full profile info
      userProfileBtn.innerHTML = `
        <div class="auth-user-avatar">
          ${user.photoURL ?
            `<img src="${user.photoURL}" alt="Profile" class="auth-avatar-img">` :
            `<div class="auth-avatar-placeholder">${avatarLetter}</div>`
          }
        </div>
        <div class="auth-user-info">
          <div class="auth-user-name">${displayName}</div>
          <div class="auth-user-email">${user.email || ''}</div>
        </div>
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" class="auth-dropdown-arrow">
          <path d="M7 10l5 5 5-5z"/>
        </svg>
      `;
    }
    
    // Update dropdown content (only for non-anonymous users)
    if (userDropdown && !user.isAnonymous) {
      userDropdown.innerHTML = `
        <div class="auth-dropdown-header">
          <div class="auth-dropdown-avatar">
            ${user.photoURL ?
              `<img src="${user.photoURL}" alt="Profile" class="auth-avatar-img">` :
              `<div class="auth-avatar-placeholder">${avatarLetter}</div>`
            }
          </div>
          <div class="auth-dropdown-info">
            <div class="auth-dropdown-name">${displayName}</div>
            <div class="auth-dropdown-email">${user.email || ''}</div>
          </div>
        </div>
        <div class="auth-dropdown-divider"></div>
        <button id="auth-sign-out-btn" class="auth-dropdown-item">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
          </svg>
          Sign out
        </button>
      `;

      // Re-attach sign out event listener
      const signOutBtn = userDropdown.querySelector('#auth-sign-out-btn');
      if (signOutBtn) {
        signOutBtn.addEventListener('click', handleSignOut);
      }
    } else if (userDropdown && user.isAnonymous) {
      // Clear dropdown content for anonymous users
      userDropdown.innerHTML = '';
    }
  } else {
    userProfileBtn.style.display = 'none';
    if (userDropdown) {
      userDropdown.classList.remove('show');
    }
  }
}

/**
 * Update authentication status indicator
 */
function updateAuthStatus() {
  const statusIndicator = document.getElementById('auth-status-indicator');
  if (!statusIndicator) return;

  if (authUIState.isAuthenticated) {
    statusIndicator.className = 'auth-status-indicator authenticated';

    if (authUIState.currentUser && authUIState.currentUser.isAnonymous) {
      statusIndicator.title = 'Signed in anonymously - Sign in with Google to save your queues permanently';
    } else {
      statusIndicator.title = 'Signed in - Your data is saved to your Google account';
    }
  } else {
    statusIndicator.className = 'auth-status-indicator not-authenticated';
    statusIndicator.title = 'Not signed in - Sign in to save your queues permanently';
  }
}



/**
 * Show authentication notification
 * @param {string} message - Notification message
 * @param {string} type - Notification type (success, error, info, warning)
 */
function showAuthNotification(message, type = 'info') {
  // Use existing notification system if available
  if (typeof showNotification === 'function') {
    showNotification(message, type);
  } else {
    console.log(`Auth notification (${type}):`, message);
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeAuthUI);
} else {
  initializeAuthUI();
}

console.log('✅ Authentication UI module loaded');
