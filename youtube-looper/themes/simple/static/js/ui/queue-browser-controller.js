// ============================================================================
// QUEUE BROWSER UI CONTROLLER
// ============================================================================

/**
 * Queue Browser UI Controller
 * Handles the public queue browser interface, queue discovery, and queue preview functionality
 * Manages queue cards display, sorting, filtering, and user interactions with public queues
 */

// Queue browser state
let currentSortBy = 'recent';
let isQueueBrowserVisible = false;

/**
 * Load and display queue browser
 * @param {boolean} forceRefresh - Force refresh the data
 */
async function loadQueueBrowser(forceRefresh = false) {
  const queueBrowserGrid = document.getElementById('queue-browser-grid');
  const queueStatsText = document.getElementById('queue-stats-text');
  
  if (!queueBrowserGrid || !queueStatsText) {
    console.warn('Queue browser elements not found');
    return;
  }

  try {
    // Show loading state
    queueBrowserGrid.innerHTML = `
      <div class="queue-browser-loading">
        <div class="loading-spinner"></div>
        <p>Discovering amazing queues from the community...</p>
      </div>
    `;

    queueStatsText.textContent = 'Loading amazing queues...';

    // Fetch queues from Firebase
    const result = await listQueuesFromFirebase(currentSortBy, 20);

    if (result.success && result.data) {
      const queues = result.data.queues || [];
      const total = result.data.total || 0;

      // Update stats
      const sortLabels = {
        recent: 'Recently Updated',
        popular: 'Most Popular',
        newest: 'Newest Created',
        longest: 'Longest Duration'
      };

      queueStatsText.innerHTML = `
        Found <strong>${total}</strong> amazing queues • Showing <strong>${sortLabels[currentSortBy]}</strong>
      `;

      // Render queue cards
      if (queues.length === 0) {
        queueBrowserGrid.innerHTML = `
          <div class="queues-empty">
            <div class="empty-icon">🌟</div>
            <h3>No public queues yet</h3>
            <p>Be the first to share an amazing queue with the community! Create a queue and make it public to get started.</p>
          </div>
        `;
      } else {
        renderQueueCards(queues);
      }
    } else {
      throw new Error(result.message || 'Failed to load queues');
    }
  } catch (error) {
    console.error('Error loading queue browser:', error);
    queueBrowserGrid.innerHTML = `
      <div class="queues-error">
        <div class="error-icon">⚠️</div>
        <h3>Unable to load public queues</h3>
        <p>We're having trouble connecting to our servers. Please check your internet connection and try again.</p>
        <button class="retry-btn" onclick="loadQueueBrowser(true)">Try Again</button>
      </div>
    `;
    queueStatsText.textContent = 'Failed to load queues';
  }
}

/**
 * Render queue cards in the grid
 * @param {Array} queues - Array of queue objects
 */
function renderQueueCards(queues) {
  const queueBrowserGrid = document.getElementById('queue-browser-grid');

  if (!queueBrowserGrid) return;

  const cardsHTML = queues.map(queue => {
    const previewHTML = queue.preview.map(video => `
      <div class="queue-preview-item">
        <img src="${video.thumbnail}" alt="${video.title}" loading="lazy" />
        <div class="queue-preview-duration">${formatDuration(video.duration)}</div>
      </div>
    `).join('');

    const lastModified = new Date(queue.lastModified);
    const isRecent = (Date.now() - lastModified.getTime()) < 24 * 60 * 60 * 1000; // 24 hours

    return `
      <div class="queue-card browser-queue" data-queue-id="${queue.queueId}">
        <div class="queue-card-header">
          <h4 class="queue-card-title">${queue.title}</h4>
          <div class="queue-card-stats">
            <div class="queue-views">👁️ ${queue.viewCount}</div>
            <div class="queue-video-count">🎵 ${queue.videoCount}</div>
          </div>
        </div>

        <div class="queue-card-preview">
          ${previewHTML}
          ${queue.videoCount > 3 ? `<div class="queue-preview-item" style="display: flex; align-items: center; justify-content: center; background: #f1f5f9; color: #64748b; font-weight: 600; font-size: 0.75rem;">+${queue.videoCount - 3}</div>` : ''}
        </div>

        <div class="queue-card-meta">
          <div class="queue-card-date">
            ${isRecent ? 'Today' : lastModified.toLocaleDateString()}
          </div>
          <div class="queue-card-duration">
            ${formatDuration(queue.totalDuration)}
          </div>
        </div>

        <div class="queue-card-actions">
          <button class="queue-load-btn" onclick="loadQueueFromBrowser('${queue.queueId}')">
            Load Queue
          </button>
          <button class="queue-preview-btn" onclick="previewQueue('${queue.queueId}')" title="Preview queue">
            Preview
          </button>
        </div>
      </div>
    `;
  }).join('');

  queueBrowserGrid.innerHTML = cardsHTML;

  // Add event listeners for browser queue card clicks
  initializeBrowserQueueCardClickHandlers();
}

/**
 * Initialize click handlers for browser queue cards to make entire card clickable
 */
function initializeBrowserQueueCardClickHandlers() {
  const queueCards = document.querySelectorAll('.queue-card.browser-queue');

  queueCards.forEach(card => {
    card.addEventListener('click', function(event) {
      // Don't trigger queue loading if clicking on interactive elements
      const clickedElement = event.target;
      const isInteractiveElement = clickedElement.closest('.queue-load-btn') ||
                                   clickedElement.closest('.queue-preview-btn') ||
                                   clickedElement.closest('button');

      if (isInteractiveElement) {
        return; // Let the specific element handle its own click
      }

      // Get queue ID and load the queue
      const queueId = this.dataset.queueId;
      if (queueId) {
        loadQueueFromBrowser(queueId);
      }
    });
  });
}

/**
 * Toggle queue browser visibility
 */
function toggleQueueBrowser() {
  const browseToggleBtn = document.getElementById('browse-toggle-btn');
  const refreshQueuesBtn = document.getElementById('refresh-queues-btn');
  const queueBrowserContainer = document.getElementById('queue-browser-container');

  if (!browseToggleBtn || !queueBrowserContainer) return;

  isQueueBrowserVisible = !isQueueBrowserVisible;

  if (isQueueBrowserVisible) {
    queueBrowserContainer.style.display = 'block';
    browseToggleBtn.textContent = '🔼 Hide Browser';
    if (refreshQueuesBtn) refreshQueuesBtn.style.display = 'inline-block';
    loadQueueBrowser();
  } else {
    queueBrowserContainer.style.display = 'none';
    browseToggleBtn.textContent = '🔍 Browse Queues';
    if (refreshQueuesBtn) refreshQueuesBtn.style.display = 'none';
  }
}

/**
 * Set queue browser sort criteria
 * @param {string} sortBy - Sort criteria
 */
function setQueueBrowserSort(sortBy) {
  currentSortBy = sortBy;
  loadQueueBrowser();
}

/**
 * Initialize queue browser event listeners
 */
function initializeQueueBrowser() {
  // Toggle queue browser visibility
  const browseToggleBtn = document.getElementById('browse-toggle-btn');
  if (browseToggleBtn) {
    browseToggleBtn.addEventListener('click', toggleQueueBrowser);
  }

  // Refresh queues
  const refreshQueuesBtn = document.getElementById('refresh-queues-btn');
  if (refreshQueuesBtn) {
    refreshQueuesBtn.addEventListener('click', function() {
      loadQueueBrowser(true);
    });
  }

  // Queue browser tabs
  document.querySelectorAll('.queue-tab').forEach(tab => {
    tab.addEventListener('click', function() {
      // Update active tab
      document.querySelectorAll('.queue-tab').forEach(t => t.classList.remove('active'));
      this.classList.add('active');

      // Update sort criteria
      const sortBy = this.getAttribute('data-sort');
      if (sortBy) {
        setQueueBrowserSort(sortBy);
      }
    });
  });

  console.log('✅ Queue browser initialized');
}

// ============================================================================
// GLOBAL QUEUE BROWSER FUNCTIONS (accessible from onclick handlers)
// ============================================================================

/**
 * Global function to load queue from browser
 * @param {string} queueId - Queue ID to load
 * @returns {Promise<boolean>} - True if successful
 */
async function loadQueueFromBrowser(queueId) {
  try {
    console.log('Loading queue from cloud...');

    if (!isFirebaseInitialized()) {
      console.log('Firebase not initialized');
      return false;
    }

    const db = getFirebaseDb();
    const docRef = db.collection('queues').doc(queueId.trim());
    const docSnapshot = await docRef.get();

    if (!docSnapshot.exists) {
      console.log('Queue not found:', queueId);
      return false;
    }

    const data = docSnapshot.data();
    const loadedData = data.queueData;



    // Update the queue variables
    setVideoQueue(loadedData.queue || []);
    setCurrentVideoIndex(loadedData.currentIndex || 0);
    setIsPlaying(false);

    // Ensure currentVideoIndex is within bounds
    if (getCurrentVideoIndex() >= getVideoQueue().length) {
      setCurrentVideoIndex(0);
    }

    // Set the loaded queue ID as current queue ID so updates sync back to shared queue
    try {
      sessionStorage.setItem('currentQueueId', queueId.trim());
      console.log('🔗 Set current queue ID to loaded queue:', queueId.trim());
    } catch (error) {
      console.warn('Could not save queue ID to session storage:', error);
    }

    // Mark that we're now viewing a shared queue
    window.isViewingSharedQueue = true;
    console.log('🔗 Now viewing shared queue - personal queue auto-save disabled');

    // Increment view count
    await docRef.update({
      'metadata.viewCount': firebase.firestore.FieldValue.increment(1)
    });

    // Update UI
    updateQueueDisplay();
    updatePlayerControls();
    saveQueueToStorage();

    // Update queue link display to show the loaded queue's link
    if (typeof updateQueueLinkDisplay === 'function') {
      updateQueueLinkDisplay();
    }

    const loadedDate = data.metadata?.lastModified?.toDate?.() || new Date();
    const queueLength = loadedData.queue ? loadedData.queue.length : 0;

    console.log(`Loaded ${queueLength} video(s) from shared queue`);

    return true;

  } catch (error) {
    console.error('Error loading queue from Firebase:', error);
    console.log('Failed to load queue from cloud:', error);
    return false;
  }
}

/**
 * Global function to preview queue
 * @param {string} queueId - Queue ID to preview
 */
function previewQueue(queueId) {
  const queueCard = document.querySelector(`[data-queue-id="${queueId}"]`);
  if (queueCard) {
    const title = queueCard.querySelector('.queue-card-title').textContent;
    console.log(`Preview queue: "${title}"`);
  }
}

// Make functions globally available
window.loadQueueFromBrowser = loadQueueFromBrowser;
window.previewQueue = previewQueue;

console.log('✅ Queue Browser module loaded');
