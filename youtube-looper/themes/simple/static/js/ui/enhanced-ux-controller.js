// ============================================================================
// ENHANCED UX CONTROLLER
// ============================================================================

/**
 * Enhanced UX Controller
 * Handles UI animations, loading states, progress indicators, and visual enhancements
 * Provides smooth transitions, feedback animations, and improved user experience features
 */

/**
 * Add CSS animations for enhanced UX
 */
function addEnhancedAnimations() {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes messageSlideOut {
      0% {
        transform: translateY(0) scale(1);
        opacity: 1;
      }
      100% {
        transform: translateY(-20px) scale(0.95);
        opacity: 0;
      }
    }

    @keyframes ripple {
      to {
        transform: scale(2);
        opacity: 0;
      }
    }

    @keyframes queueItemAdd {
      0% {
        transform: translateX(-20px);
        opacity: 0;
      }
      100% {
        transform: translateX(0);
        opacity: 1;
      }
    }

    .queue-item.newly-added {
      animation: queueItemAdd 0.5s ease-out;
    }

    .search-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transition: all 0.2s ease;
    }

    .queue-item:hover {
      transform: translateY(-1px);
      transition: all 0.2s ease;
    }

    .btn:hover {
      transform: translateY(-1px);
      transition: all 0.2s ease;
    }

    .loading-spinner {
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `;
  document.head.appendChild(style);
}



/**
 * Add smooth scroll to queue when item is added
 */
function scrollToNewQueueItem() {
  const queueContainer = document.getElementById('queue-container');
  if (queueContainer) {
    queueContainer.scrollTop = queueContainer.scrollHeight;
  }
}

/**
 * Add visual feedback for button interactions (ripple effect)
 */
function initializeRippleEffects() {
  document.addEventListener('click', function(e) {
    if (e.target.classList.contains('btn')) {
      const button = e.target;
      
      // Create ripple effect
      const ripple = document.createElement('span');
      const rect = button.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      const x = e.clientX - rect.left - size / 2;
      const y = e.clientY - rect.top - size / 2;

      ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: rgba(255, 255, 255, 0.4);
        border-radius: 50%;
        transform: scale(0);
        animation: ripple 0.6s ease-out;
        pointer-events: none;
      `;

      button.style.position = 'relative';
      button.style.overflow = 'hidden';
      button.appendChild(ripple);

      setTimeout(() => {
        ripple.remove();
      }, 600);
    }
  });
}

/**
 * Add loading states for better UX
 * @param {HTMLElement} element - Element to show loading state
 * @param {string} loadingText - Loading text to display
 */
function showLoadingState(element, loadingText = 'Loading...') {
  if (!element) return;

  const originalContent = element.innerHTML;
  element.dataset.originalContent = originalContent;
  
  element.innerHTML = `
    <div class="loading-spinner" style="display: inline-block; width: 16px; height: 16px; border: 2px solid #f3f3f3; border-top: 2px solid #3498db; border-radius: 50%; margin-right: 8px;"></div>
    ${loadingText}
  `;
  element.disabled = true;
}

/**
 * Hide loading state and restore original content
 * @param {HTMLElement} element - Element to restore
 */
function hideLoadingState(element) {
  if (!element || !element.dataset.originalContent) return;

  element.innerHTML = element.dataset.originalContent;
  element.disabled = false;
  delete element.dataset.originalContent;
}

/**
 * Add progress indicators for long operations
 * @param {string} containerId - Container element ID
 * @param {number} progress - Progress percentage (0-100)
 */
function updateProgress(containerId, progress) {
  const container = document.getElementById(containerId);
  if (!container) return;

  let progressBar = container.querySelector('.progress-bar');
  if (!progressBar) {
    progressBar = document.createElement('div');
    progressBar.className = 'progress-bar';
    progressBar.style.cssText = `
      width: 100%;
      height: 4px;
      background: #f0f0f0;
      border-radius: 2px;
      overflow: hidden;
      margin: 10px 0;
    `;
    
    const progressFill = document.createElement('div');
    progressFill.className = 'progress-fill';
    progressFill.style.cssText = `
      height: 100%;
      background: linear-gradient(90deg, #4caf50, #8bc34a);
      border-radius: 2px;
      transition: width 0.3s ease;
      width: 0%;
    `;
    
    progressBar.appendChild(progressFill);
    container.appendChild(progressBar);
  }

  const progressFill = progressBar.querySelector('.progress-fill');
  if (progressFill) {
    progressFill.style.width = `${Math.min(100, Math.max(0, progress))}%`;
  }

  // Remove progress bar when complete
  if (progress >= 100) {
    setTimeout(() => {
      if (progressBar.parentNode) {
        progressBar.parentNode.removeChild(progressBar);
      }
    }, 500);
  }
}

/**
 * Initialize enhanced UX features
 */
function initializeEnhancedUX() {
  console.log('Initializing enhanced UX features...');

  addEnhancedAnimations();
  initializeRippleEffects();

  console.log('✅ Enhanced UX features initialized');
}

console.log('✅ Enhanced UX module loaded');
