// ============================================================================
// PERSONAL QUEUES UTILS COMPONENT
// ============================================================================

/**
 * Personal Queues Utils Component
 * Provides utility functions, helpers, and common functionality
 * Shared utilities used across personal queue components
 */

/**
 * Escape HTML to prevent XSS attacks
 * @param {string} text - Text to escape
 * @returns {string} Escaped text
 */
function escapeHtml(text) {
  if (!text) return '';
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

/**
 * Format relative time from date
 * @param {Date|string|number} date - Date to format
 * @returns {string} Formatted relative time
 */
function formatRelativeTime(date) {
  if (!date) return 'Unknown';
  
  const now = new Date();
  const targetDate = date instanceof Date ? date : new Date(date);
  
  // Check if date is valid
  if (isNaN(targetDate.getTime())) {
    return 'Unknown';
  }
  
  const diffMs = now - targetDate;
  const diffMins = Math.floor(diffMs / 60000);
  const diffHours = Math.floor(diffMs / 3600000);
  const diffDays = Math.floor(diffMs / 86400000);
  const diffWeeks = Math.floor(diffMs / 604800000);
  const diffMonths = Math.floor(diffMs / 2629746000);

  if (diffMins < 1) return 'Just now';
  if (diffMins < 60) return `${diffMins} minute${diffMins === 1 ? '' : 's'} ago`;
  if (diffHours < 24) return `${diffHours} hour${diffHours === 1 ? '' : 's'} ago`;
  if (diffDays < 7) return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`;
  if (diffWeeks < 4) return `${diffWeeks} week${diffWeeks === 1 ? '' : 's'} ago`;
  if (diffMonths < 12) return `${diffMonths} month${diffMonths === 1 ? '' : 's'} ago`;

  return targetDate.toLocaleDateString();
}

/**
 * Format duration in seconds to human readable format
 * @param {number} seconds - Duration in seconds
 * @returns {string} Formatted duration (e.g., "3:45", "1:23:45")
 */
function formatDuration(seconds) {
  if (!seconds || seconds < 0) return '0:00';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
}

/**
 * Parse duration string to seconds
 * @param {string} durationStr - Duration string (e.g., "3:45", "1:23:45")
 * @returns {number} Duration in seconds
 */
function parseDuration(durationStr) {
  if (!durationStr) return 0;
  
  const parts = durationStr.split(':').map(part => parseInt(part, 10));
  
  if (parts.length === 2) {
    // MM:SS format
    return parts[0] * 60 + parts[1];
  } else if (parts.length === 3) {
    // HH:MM:SS format
    return parts[0] * 3600 + parts[1] * 60 + parts[2];
  }
  
  return 0;
}

/**
 * Generate a unique ID
 * @param {string} prefix - Optional prefix for the ID
 * @returns {string} Unique ID
 */
function generateUniqueId(prefix = 'id') {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `${prefix}_${timestamp}_${random}`;
}

/**
 * Debounce function to limit function calls
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 */
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * Throttle function to limit function calls
 * @param {Function} func - Function to throttle
 * @param {number} limit - Time limit in milliseconds
 * @returns {Function} Throttled function
 */
function throttle(func, limit) {
  let inThrottle;
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * Deep clone an object
 * @param {*} obj - Object to clone
 * @returns {*} Cloned object
 */
function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime());
  if (obj instanceof Array) return obj.map(item => deepClone(item));
  if (typeof obj === 'object') {
    const clonedObj = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
}

/**
 * Check if user is authenticated
 * @returns {Promise<boolean>} True if user is authenticated
 */
async function isUserAuthenticated() {
  try {
    const userId = await getCurrentUserId();
    return !!userId;
  } catch (error) {
    console.warn('Could not check authentication status:', error);
    return false;
  }
}

/**
 * Get current view from navigation
 * @returns {string} Current view name
 */
function getCurrentView() {
  try {
    // Try to get from navigation state
    if (typeof window.getCurrentView === 'function') {
      return window.getCurrentView();
    }
    
    // Fallback: check active navigation item
    const activeNavItem = document.querySelector('.nav-item.active');
    if (activeNavItem) {
      return activeNavItem.dataset.view || 'search';
    }
    
    return 'search';
  } catch (error) {
    console.warn('Could not get current view:', error);
    return 'search';
  }
}

/**
 * Validate queue title
 * @param {string} title - Queue title to validate
 * @returns {Object} Validation result with isValid and error
 */
function validateQueueTitle(title) {
  if (!title || typeof title !== 'string') {
    return { isValid: false, error: 'Title is required' };
  }
  
  const trimmed = title.trim();
  
  if (trimmed.length === 0) {
    return { isValid: false, error: 'Title cannot be empty' };
  }
  
  if (trimmed.length > 100) {
    return { isValid: false, error: 'Title cannot be longer than 100 characters' };
  }
  
  // Check for invalid characters
  const invalidChars = /[<>:"\/\\|?*]/;
  if (invalidChars.test(trimmed)) {
    return { isValid: false, error: 'Title contains invalid characters' };
  }
  
  return { isValid: true, title: trimmed };
}

/**
 * Format file size in bytes to human readable format
 * @param {number} bytes - Size in bytes
 * @returns {string} Formatted size
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Get queue statistics summary
 * @param {Array} queues - Array of queue objects
 * @returns {Object} Statistics summary
 */
function getQueuesStatsSummary(queues) {
  if (!queues || !Array.isArray(queues)) {
    return {
      totalQueues: 0,
      totalVideos: 0,
      totalDuration: 0,
      formattedTotalDuration: '0:00',
      publicQueues: 0,
      privateQueues: 0,
      averageVideosPerQueue: 0
    };
  }

  const totalQueues = queues.length;
  const totalVideos = queues.reduce((sum, queue) => sum + (queue.videoCount || 0), 0);
  const totalDuration = queues.reduce((sum, queue) => sum + (queue.totalDuration || 0), 0);
  const publicQueues = queues.filter(queue => queue.isPublic).length;
  const privateQueues = totalQueues - publicQueues;
  const averageVideosPerQueue = totalQueues > 0 ? Math.round(totalVideos / totalQueues) : 0;

  return {
    totalQueues,
    totalVideos,
    totalDuration,
    formattedTotalDuration: formatDuration(totalDuration),
    publicQueues,
    privateQueues,
    averageVideosPerQueue
  };
}

/**
 * Sort queues by different criteria
 * @param {Array} queues - Array of queue objects
 * @param {string} sortBy - Sort criteria ('title', 'date', 'videos', 'duration')
 * @param {string} order - Sort order ('asc', 'desc')
 * @returns {Array} Sorted queues
 */
function sortQueues(queues, sortBy = 'date', order = 'desc') {
  if (!queues || !Array.isArray(queues)) return [];

  const sorted = [...queues].sort((a, b) => {
    let aValue, bValue;

    switch (sortBy) {
      case 'title':
        aValue = (a.title || '').toLowerCase();
        bValue = (b.title || '').toLowerCase();
        break;
      case 'videos':
        aValue = a.videoCount || 0;
        bValue = b.videoCount || 0;
        break;
      case 'duration':
        aValue = a.totalDuration || 0;
        bValue = b.totalDuration || 0;
        break;
      case 'date':
      default:
        aValue = a.lastModified ? new Date(a.lastModified).getTime() : 0;
        bValue = b.lastModified ? new Date(b.lastModified).getTime() : 0;
        break;
    }

    if (aValue < bValue) return order === 'asc' ? -1 : 1;
    if (aValue > bValue) return order === 'asc' ? 1 : -1;
    return 0;
  });

  return sorted;
}

/**
 * Filter queues by search term
 * @param {Array} queues - Array of queue objects
 * @param {string} searchTerm - Search term
 * @returns {Array} Filtered queues
 */
function filterQueues(queues, searchTerm) {
  if (!queues || !Array.isArray(queues) || !searchTerm) return queues;

  const term = searchTerm.toLowerCase().trim();
  
  return queues.filter(queue => {
    const title = (queue.title || '').toLowerCase();
    return title.includes(term);
  });
}

// Export functions for global access
window.escapeHtml = escapeHtml;
window.formatRelativeTime = formatRelativeTime;
window.formatDuration = formatDuration;
window.parseDuration = parseDuration;
window.generateUniqueId = generateUniqueId;
window.debounce = debounce;
window.throttle = throttle;
window.deepClone = deepClone;
window.isUserAuthenticated = isUserAuthenticated;
window.getCurrentView = getCurrentView;
window.validateQueueTitle = validateQueueTitle;
window.formatFileSize = formatFileSize;
window.getQueuesStatsSummary = getQueuesStatsSummary;
window.sortQueues = sortQueues;
window.filterQueues = filterQueues;

console.log('✅ Personal Queues Utils component loaded');
