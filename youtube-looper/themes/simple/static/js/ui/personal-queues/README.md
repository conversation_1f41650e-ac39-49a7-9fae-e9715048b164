# Personal Queues Components

This directory contains the modular personal queues system, broken down into feature-based components for better maintainability and organization.

## Directory Structure

```
personal-queues/
├── display/
│   └── queue-display.js          # Queue cards rendering and visualization
├── privacy/
│   └── queue-privacy.js          # Privacy toggle and management
├── loading/
│   └── queue-loading.js          # Data loading and caching
├── actions/
│   └── queue-actions.js          # Queue operations (delete, duplicate, etc.)
├── events/
│   └── queue-events.js           # Event handling and interactions
├── utils/
│   └── queue-utils.js            # Utility functions and helpers
├── index.js                      # Main controller and API
└── README.md                     # This file
```

## Component Overview

### 1. Display Component (`display/queue-display.js`)
**Purpose**: Handles queue cards rendering, visualization, and UI state management
**Key Functions**:
- `displayPersonalQueues()` - Main rendering function
- `createQueueCardHTML()` - Generate individual queue cards
- `showLoadingState()` / `showErrorState()` / `showEmptyState()` - UI states
- `updateQueueCard()` - Update specific queue cards

**Responsibilities**:
- Queue card HTML generation
- Loading/error/empty state display
- Queue card updates and highlighting
- Visual feedback and animations

### 2. Privacy Component (`privacy/queue-privacy.js`)
**Purpose**: Handles privacy toggle functionality and public/private queue management
**Key Functions**:
- `initializeQueuePrivacyToggles()` - Set up privacy controls
- `updateQueuePrivacy()` - Update privacy in Firebase
- `updateQueueCardPrivacy()` - Update UI after privacy change
- `batchUpdateQueuePrivacy()` - Bulk privacy updates

**Responsibilities**:
- Privacy toggle event handling
- Firebase privacy updates
- Public/private queue management
- Share link display management

### 3. Loading Component (`loading/queue-loading.js`)
**Purpose**: Handles data loading, caching, and Firebase integration
**Key Functions**:
- `loadPersonalQueues()` - Load queues with caching
- `loadPersonalQueue()` - Load specific queue
- `invalidatePersonalQueuesCache()` - Cache management
- `refreshPersonalQueues()` - Force refresh

**Responsibilities**:
- Firebase data loading
- Intelligent caching system
- Authentication handling
- Error handling and recovery

### 4. Actions Component (`actions/queue-actions.js`)
**Purpose**: Handles queue operations like delete, duplicate, export, etc.
**Key Functions**:
- `deletePersonalQueue()` - Delete with confirmation
- `duplicatePersonalQueue()` - Create queue copies
- `renamePersonalQueue()` - Rename queues
- `exportPersonalQueue()` - Export as JSON

**Responsibilities**:
- Queue CRUD operations
- User confirmation dialogs
- Batch operations
- Data export functionality

### 5. Events Component (`events/queue-events.js`)
**Purpose**: Handles event listeners, user interactions, and event coordination
**Key Functions**:
- `initializeQueueLinkCopyButtons()` - Copy link functionality
- `initializeQueueCardClickHandlers()` - Card click handling
- `initializeQueueCardContextMenu()` - Right-click context menu
- `initializePersonalQueuesKeyboardShortcuts()` - Keyboard shortcuts

**Responsibilities**:
- User interaction handling
- Context menu management
- Keyboard shortcuts
- Event coordination

### 6. Utils Component (`utils/queue-utils.js`)
**Purpose**: Provides utility functions, helpers, and common functionality
**Key Functions**:
- `escapeHtml()` - XSS prevention
- `formatRelativeTime()` / `formatDuration()` - Time formatting
- `validateQueueTitle()` - Input validation
- `sortQueues()` / `filterQueues()` - Data manipulation

**Responsibilities**:
- Security utilities
- Data formatting
- Validation functions
- Common helpers

### 7. Main Controller (`index.js`)
**Purpose**: Coordinates all components and provides unified API
**Key Functions**:
- `initializePersonalQueues()` - Initialize all components
- `PersonalQueuesAPI` - Unified API object
- Event coordination and state management

## API Usage

### Unified Personal Queues API

```javascript
// Access the unified API
const pq = window.PersonalQueues;

// Get current state
const state = pq.getState();
console.log(state.isAuthenticated); // Authentication status
console.log(state.cache); // Cache statistics

// Load queues
pq.load();           // Load with cache
pq.load(true);       // Force refresh
pq.loadQueue('id');  // Load specific queue

// Queue actions
pq.delete('queueId');           // Delete queue
pq.duplicate('queueId');        // Duplicate queue
pq.rename('queueId');           // Rename queue
pq.export('queueId');           // Export queue

// Privacy management
pq.togglePrivacy('queueId');              // Toggle privacy
pq.updatePrivacy('queueId', true);        // Make public

// Cache management
pq.invalidateCache();           // Clear cache
const cacheStats = pq.getCacheStats();    // Get cache info

// Utilities
pq.formatTime(date);            // Format relative time
pq.formatDuration(seconds);     // Format duration
pq.validateTitle(title);        // Validate queue title

// Interface management
pq.refresh();                   // Refresh entire interface
```

### Individual Component Functions

```javascript
// Display functions
displayPersonalQueues(queues);
createQueueCardHTML(queue);
showLoadingState(container, message);
updateQueueCard(queueId, updates);

// Privacy functions
initializeQueuePrivacyToggles();
updateQueuePrivacy(queueId, isPublic);
toggleQueuePrivacy(queueId);

// Loading functions
loadPersonalQueues(forceRefresh);
loadPersonalQueue(queueId);
invalidatePersonalQueuesCache();

// Action functions
deletePersonalQueue(queueId);
duplicatePersonalQueue(queueId);
renamePersonalQueue(queueId);
exportPersonalQueue(queueId);

// Event functions
initializeQueueLinkCopyButtons();
initializeQueueCardClickHandlers();
copyQueueLinkToClipboard(queueId);

// Utility functions
escapeHtml(text);
formatRelativeTime(date);
validateQueueTitle(title);
sortQueues(queues, sortBy, order);
```

## Event System

### Personal Queue State Changes
```javascript
// Listen for personal queue state changes
document.addEventListener('personalQueueStateChanged', (event) => {
  const { type, data, state } = event.detail;
  console.log('Personal queue changed:', type, data);
});

// Emit personal queue state changes
emitPersonalQueueStateChange('queue_deleted', { queueId: 'abc123' });
```

### Authentication Changes
```javascript
// Listen for auth state changes
document.addEventListener('authStateChanged', (event) => {
  const { isAuthenticated, userId } = event.detail;
  // Personal queues will automatically handle this
});
```

## Component Dependencies

### External Dependencies
- `getCurrentUserId()` - Authentication
- `window.personalQueueRepo` - Data repository
- `window.personalQueueService` - Business logic
- `PersonalQueueModel` / `QueueMetadataModel` - Data models
- `showNotification()` - User feedback (optional)

### Internal Dependencies
```
index.js
├── display/queue-display.js
├── privacy/queue-privacy.js
├── loading/queue-loading.js
├── actions/queue-actions.js
├── events/queue-events.js
└── utils/queue-utils.js
```

## Loading Order

Components must be loaded in this order:

1. **Individual Components** (can be loaded in parallel):
   - `display/queue-display.js`
   - `privacy/queue-privacy.js`
   - `loading/queue-loading.js`
   - `actions/queue-actions.js`
   - `events/queue-events.js`
   - `utils/queue-utils.js`

2. **Main Controller**:
   - `index.js` (coordinates all components)

## Caching System

### Cache Features
- **Intelligent Caching**: 30-second cache with user-specific data
- **Cache Invalidation**: Automatic invalidation on data changes
- **Cache Statistics**: Detailed cache information and metrics
- **Configurable**: Adjustable cache duration

### Cache Usage
```javascript
// Check cache status
const stats = getCacheStats();
console.log(stats.isValid); // Is cache valid?
console.log(stats.ageSeconds); // Cache age in seconds

// Configure cache
setCacheMaxAge(60000); // Set to 60 seconds

// Manual cache management
invalidatePersonalQueuesCache(); // Clear cache
```

## Security Features

### XSS Prevention
- All user input is escaped using `escapeHtml()`
- HTML content is properly sanitized
- Safe DOM manipulation practices

### Input Validation
- Queue titles are validated for length and characters
- User input is sanitized before processing
- Error handling for invalid data

## Performance Optimizations

### Implemented Optimizations
- **Intelligent Caching**: Reduces Firebase calls
- **Lazy Loading**: Images loaded with `loading="lazy"`
- **Debounced Events**: Prevents excessive event handling
- **Efficient DOM Updates**: Minimal reflows and repaints
- **Async Operations**: Non-blocking UI updates

### Memory Management
- Event listeners are properly managed
- No memory leaks from closures
- Efficient data structures
- Cleanup on component destruction

## Future Enhancements

### Planned Features
1. **Drag & Drop Reordering** - Visual queue reordering
2. **Bulk Operations** - Select multiple queues for batch actions
3. **Advanced Search** - Search within queue content
4. **Queue Templates** - Predefined queue templates
5. **Import/Export** - Multiple format support
6. **Queue Analytics** - Usage statistics and insights

### Architecture Improvements
1. **Virtual Scrolling** - Handle very large queue lists
2. **Offline Support** - Work without internet connection
3. **Real-time Sync** - Live updates across devices
4. **Advanced Caching** - Smarter cache strategies

## Migration Notes

This modular system replaces the monolithic `personal-queues-ui.js` file with the following benefits:

- ✅ **Better Organization** - Logical separation of concerns
- ✅ **Easier Maintenance** - Smaller, focused files
- ✅ **Improved Testing** - Components can be tested independently
- ✅ **Better Performance** - Optimized loading and execution
- ✅ **Enhanced Reusability** - Components can be reused elsewhere
- ✅ **Clearer Dependencies** - Explicit component relationships

All existing functionality is preserved while providing a much more maintainable and scalable architecture.
