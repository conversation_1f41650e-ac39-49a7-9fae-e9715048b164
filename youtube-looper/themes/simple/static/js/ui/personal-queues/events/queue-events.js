// ============================================================================
// PERSONAL QUEUES EVENTS COMPONENT
// ============================================================================

/**
 * Personal Queues Events Component
 * Handles event listeners, user interactions, and event coordination
 * Manages click handlers, copy buttons, and other user interactions
 */

/**
 * Initialize queue link copy buttons
 */
function initializeQueueLinkCopyButtons() {
  document.addEventListener('click', function(e) {
    const copyBtn = e.target.closest('.copy-queue-link-btn');
    
    if (copyBtn) {
      e.preventDefault();
      e.stopPropagation();
      
      const queueId = copyBtn.getAttribute('data-queue-id');
      if (queueId) {
        copyQueueLinkToClipboard(queueId, copyBtn);
      }
    }
  });
}

/**
 * Copy queue link to clipboard
 * @param {string} queueId - Queue ID
 * @param {HTMLElement} button - Copy button element
 */
async function copyQueueLinkToClipboard(queueId, button) {
  const queueUrl = `${window.location.origin}${window.location.pathname}?q=${queueId}`;

  try {
    await navigator.clipboard.writeText(queueUrl);
    
    // Show visual feedback
    showCopyButtonFeedback(button);
    
    console.log(`📋 Copied queue link for: ${queueId}`);
    
    // Show notification if available
    if (typeof showNotification === 'function') {
      showNotification('Queue link copied to clipboard', 'success');
    }
  } catch (error) {
    console.error('Failed to copy queue link:', error);
    
    // Fallback: show URL in prompt
    prompt('Copy this queue link:', queueUrl);
    
    if (typeof showNotification === 'function') {
      showNotification('Queue link ready to copy', 'info');
    }
  }
}

/**
 * Show visual feedback for copy button
 * @param {HTMLElement} button - Copy button element
 */
function showCopyButtonFeedback(button) {
  if (!button) return;

  const originalContent = button.innerHTML;
  button.innerHTML = '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg>';
  button.classList.add('copied');
  
  setTimeout(() => {
    button.innerHTML = originalContent;
    button.classList.remove('copied');
  }, 2000);
}

/**
 * Add copy button event listener to a specific button
 * @param {HTMLElement} button - Copy button element
 */
function addCopyButtonEventListener(button) {
  if (!button) return;

  button.addEventListener('click', function(e) {
    e.preventDefault();
    e.stopPropagation();
    
    const queueId = this.getAttribute('data-queue-id');
    if (queueId) {
      copyQueueLinkToClipboard(queueId, this);
    }
  });
}

/**
 * Initialize queue card click handlers
 */
function initializeQueueCardClickHandlers() {
  document.addEventListener('click', function(e) {
    const queueCard = e.target.closest('.queue-card.personal-queue');
    
    if (queueCard && !e.target.closest('.queue-actions') && !e.target.closest('.queue-privacy-controls') && !e.target.closest('.queue-link-section')) {
      const queueId = queueCard.getAttribute('data-queue-id');
      if (queueId) {
        handleQueueCardClick(queueId, queueCard);
      }
    }
  });
}

/**
 * Handle queue card click
 * @param {string} queueId - Queue ID
 * @param {HTMLElement} queueCard - Queue card element
 */
function handleQueueCardClick(queueId, queueCard) {
  console.log('🎵 Queue card clicked:', queueId);
  
  // Add visual feedback
  queueCard.classList.add('clicked');
  setTimeout(() => {
    queueCard.classList.remove('clicked');
  }, 200);

  // Load the personal queue
  if (typeof loadPersonalQueue === 'function') {
    loadPersonalQueue(queueId);
  }
}

/**
 * Initialize context menu for queue cards
 */
function initializeQueueCardContextMenu() {
  document.addEventListener('contextmenu', function(e) {
    const queueCard = e.target.closest('.queue-card.personal-queue');
    
    if (queueCard) {
      e.preventDefault();
      const queueId = queueCard.getAttribute('data-queue-id');
      if (queueId) {
        showQueueContextMenu(e, queueId, queueCard);
      }
    }
  });

  // Close context menu when clicking elsewhere
  document.addEventListener('click', function(e) {
    const contextMenu = document.getElementById('queue-context-menu');
    if (contextMenu && !e.target.closest('#queue-context-menu')) {
      hideQueueContextMenu();
    }
  });
}

/**
 * Show context menu for queue card
 * @param {MouseEvent} event - Mouse event
 * @param {string} queueId - Queue ID
 * @param {HTMLElement} queueCard - Queue card element
 */
function showQueueContextMenu(event, queueId, queueCard) {
  // Remove existing context menu
  hideQueueContextMenu();

  const contextMenu = document.createElement('div');
  contextMenu.id = 'queue-context-menu';
  contextMenu.className = 'queue-context-menu';
  
  const isPublic = queueCard.querySelector('.queue-privacy-toggle')?.checked || false;
  
  contextMenu.innerHTML = `
    <div class="context-menu-item" data-action="load">
      <span class="context-menu-icon">▶</span>
      Load Queue
    </div>
    <div class="context-menu-item" data-action="rename">
      <span class="context-menu-icon">✏️</span>
      Rename
    </div>
    <div class="context-menu-item" data-action="duplicate">
      <span class="context-menu-icon">📋</span>
      Duplicate
    </div>
    <div class="context-menu-item" data-action="export">
      <span class="context-menu-icon">📤</span>
      Export
    </div>
    ${isPublic ? `
    <div class="context-menu-item" data-action="copy-link">
      <span class="context-menu-icon">🔗</span>
      Copy Link
    </div>
    ` : ''}
    <div class="context-menu-separator"></div>
    <div class="context-menu-item context-menu-item-danger" data-action="delete">
      <span class="context-menu-icon">🗑️</span>
      Delete
    </div>
  `;

  // Position the context menu
  contextMenu.style.left = `${event.pageX}px`;
  contextMenu.style.top = `${event.pageY}px`;

  document.body.appendChild(contextMenu);

  // Add event listeners to menu items
  contextMenu.addEventListener('click', function(e) {
    const menuItem = e.target.closest('.context-menu-item');
    if (menuItem) {
      const action = menuItem.getAttribute('data-action');
      handleContextMenuAction(action, queueId);
      hideQueueContextMenu();
    }
  });

  // Position adjustment if menu goes off screen
  const rect = contextMenu.getBoundingClientRect();
  if (rect.right > window.innerWidth) {
    contextMenu.style.left = `${event.pageX - rect.width}px`;
  }
  if (rect.bottom > window.innerHeight) {
    contextMenu.style.top = `${event.pageY - rect.height}px`;
  }
}

/**
 * Hide context menu
 */
function hideQueueContextMenu() {
  const contextMenu = document.getElementById('queue-context-menu');
  if (contextMenu) {
    contextMenu.remove();
  }
}

/**
 * Handle context menu action
 * @param {string} action - Action to perform
 * @param {string} queueId - Queue ID
 */
function handleContextMenuAction(action, queueId) {
  switch (action) {
    case 'load':
      if (typeof loadPersonalQueue === 'function') {
        loadPersonalQueue(queueId);
      }
      break;
    case 'rename':
      if (typeof renamePersonalQueue === 'function') {
        renamePersonalQueue(queueId);
      }
      break;
    case 'duplicate':
      if (typeof duplicatePersonalQueue === 'function') {
        duplicatePersonalQueue(queueId);
      }
      break;
    case 'export':
      if (typeof exportPersonalQueue === 'function') {
        exportPersonalQueue(queueId);
      }
      break;
    case 'copy-link':
      copyQueueLinkToClipboard(queueId);
      break;
    case 'delete':
      if (typeof deletePersonalQueue === 'function') {
        deletePersonalQueue(queueId);
      }
      break;
    default:
      console.log('Unknown context menu action:', action);
  }
}

/**
 * Initialize keyboard shortcuts for personal queues
 */
function initializePersonalQueuesKeyboardShortcuts() {
  document.addEventListener('keydown', function(e) {
    // Only handle shortcuts when not typing in input fields
    if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
      return;
    }

    // Only handle shortcuts when on personal queues view
    if (getCurrentView() !== 'personal') {
      return;
    }

    switch (e.code) {
      case 'KeyR':
        if (e.ctrlKey || e.metaKey) {
          e.preventDefault();
          if (typeof refreshPersonalQueues === 'function') {
            refreshPersonalQueues();
          }
        }
        break;
      
      case 'KeyN':
        if (e.ctrlKey || e.metaKey) {
          e.preventDefault();
          // Future: Create new queue
          console.log('New queue shortcut (not implemented yet)');
        }
        break;
        
      case 'Delete':
      case 'Backspace':
        // Future: Delete selected queues
        console.log('Delete shortcut (not implemented yet)');
        break;
    }
  });
}

/**
 * Initialize drag and drop for queue reordering (future feature)
 */
function initializeQueueDragAndDrop() {
  // Future implementation for drag and drop reordering
  console.log('🚧 Drag and drop not implemented yet');
}

/**
 * Initialize all personal queue event handlers
 */
function initializePersonalQueueEvents() {
  initializeQueueLinkCopyButtons();
  initializeQueueCardClickHandlers();
  initializeQueueCardContextMenu();
  initializePersonalQueuesKeyboardShortcuts();
  
  console.log('🎯 Personal queue events initialized');
}

// Export functions for global access
window.initializeQueueLinkCopyButtons = initializeQueueLinkCopyButtons;
window.initializeQueueCardClickHandlers = initializeQueueCardClickHandlers;
window.initializeQueueCardContextMenu = initializeQueueCardContextMenu;
window.initializePersonalQueuesKeyboardShortcuts = initializePersonalQueuesKeyboardShortcuts;
window.initializePersonalQueueEvents = initializePersonalQueueEvents;
window.addCopyButtonEventListener = addCopyButtonEventListener;
window.copyQueueLinkToClipboard = copyQueueLinkToClipboard;

console.log('✅ Personal Queues Events component loaded');
