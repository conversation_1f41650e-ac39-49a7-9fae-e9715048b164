// ============================================================================
// PERSONAL QUEUES LOADING COMPONENT
// ============================================================================

/**
 * Personal Queues Loading Component
 * Handles data loading, caching, and Firebase integration
 * Manages queue data fetching and cache management
 */

// Cache for personal queues to avoid unnecessary re-fetching
let personalQueuesCache = {
  data: null,
  userId: null,
  timestamp: null,
  maxAge: 30000 // 30 seconds cache
};

/**
 * Invalidate the personal queues cache
 */
function invalidatePersonalQueuesCache() {
  console.log('🗑️ Invalidating personal queues cache');
  personalQueuesCache.data = null;
  personalQueuesCache.timestamp = null;
}

/**
 * Check if cache is valid for user
 * @param {string} userId - User ID to check
 * @returns {boolean} True if cache is valid
 */
function isCacheValid(userId) {
  const now = Date.now();
  return personalQueuesCache.data &&
         personalQueuesCache.userId === userId &&
         personalQueuesCache.timestamp &&
         (now - personalQueuesCache.timestamp) < personalQueuesCache.maxAge;
}

/**
 * Update cache with new data
 * @param {Array} data - Queue data to cache
 * @param {string} userId - User ID
 */
function updateCache(data, userId) {
  personalQueuesCache = {
    data: data,
    userId: userId,
    timestamp: Date.now(),
    maxAge: personalQueuesCache.maxAge
  };
}

/**
 * Load personal queues for the current user
 * @param {boolean} forceRefresh - Force refresh from Firebase, bypassing cache
 */
async function loadPersonalQueues(forceRefresh = false) {
  const container = document.getElementById('personal-queues-list');
  if (!container) return;

  try {
    const userId = await getCurrentUserId();
    console.log('👤 Loading personal queues for user:', userId);

    if (!userId) {
      console.log('❌ No user ID found');
      if (typeof showSignInRequiredState === 'function') {
        showSignInRequiredState(container);
      }
      return;
    }

    // Check cache first (unless force refresh is requested)
    if (!forceRefresh && isCacheValid(userId)) {
      console.log('📋 Using cached personal queues data');
      const queues = personalQueuesCache.data;

      if (queues && queues.length > 0) {
        console.log('✅ Displaying', queues.length, 'cached personal queues');
        if (typeof displayPersonalQueues === 'function') {
          displayPersonalQueues(queues);
        }
      } else {
        console.log('📝 No cached personal queues found');
        if (typeof showEmptyState === 'function') {
          showEmptyState(
            container,
            'No personal queues yet',
            'Create and save your first queue to see it here!'
          );
        }
      }
      return;
    }

    // Show loading state only if we don't have cached data to display
    if (!isCacheValid(userId)) {
      if (typeof showLoadingState === 'function') {
        showLoadingState(container, 'Loading your personal queues...');
      }
    }

    // Load personal queues from Firebase
    const queues = await loadPersonalQueuesFromFirebase(userId);
    console.log('📋 Loaded queues from Firebase:', queues);

    // Update cache
    updateCache(queues, userId);

    if (queues && queues.length > 0) {
      console.log('✅ Displaying', queues.length, 'personal queues');
      if (typeof displayPersonalQueues === 'function') {
        displayPersonalQueues(queues);
      }
    } else {
      console.log('📝 No personal queues found');
      if (typeof showEmptyState === 'function') {
        showEmptyState(
          container,
          'No personal queues yet',
          'Start building your music collection! Search for videos and create your first personal queue.'
        );
      }
    }
  } catch (error) {
    console.error('❌ Error loading personal queues:', error);
    if (typeof handleUIError === 'function') {
      handleUIError(error, 'load personal queues', container);
    }
  }
}

/**
 * Load personal queues from Firebase using the new model system
 * @param {string} userId - User ID
 * @returns {Promise<Array<Object>>} Array of personal queue data
 */
async function loadPersonalQueuesFromFirebase(userId) {
  try {
    console.log('🔍 Loading personal queues for userId:', userId);

    if (!isFirebaseInitialized()) {
      throw new Error('Firebase not initialized');
    }

    // Use the new service layer to get personal queues
    const queues = await window.personalQueueService.listQueuesForUser(userId);
    
    console.log('✅ Loaded queues using new service:', queues.length);
    
    // Transform to display format using model methods
    return queues.map(queue => ({
      id: queue.id,
      title: queue.title,
      videoCount: queue.videoCount,
      lastModified: queue.lastModified,
      thumbnail: queue.thumbnail || (queue.preview && queue.preview[0] ? queue.preview[0].thumbnail : null),
      isPublic: queue.isPublic || false
    }));
    
  } catch (error) {
    console.error('Error loading personal queues from Firebase:', error);
    return [];
  }
}

/**
 * Load a specific personal queue
 * @param {string} queueId - ID of the personal queue to load
 */
async function loadPersonalQueue(queueId) {
  try {
    console.log('🎵 Loading personal queue:', queueId);

    const userId = await getCurrentUserId();
    if (!userId) {
      console.log('❌ User not signed in');
      if (typeof showNotification === 'function') {
        showNotification('Please sign in to load personal queues', 'warning');
      }
      return;
    }

    // Show loading feedback
    if (typeof showNotification === 'function') {
      showNotification('Loading personal queue...', 'info');
    }

    // Use the new repository to load the personal queue
    const personalQueue = await window.personalQueueRepo.load(queueId);

    if (!personalQueue) {
      console.log('❌ Personal queue not found:', queueId);
      if (typeof showNotification === 'function') {
        showNotification('Queue not found or no longer available', 'error');
      }
      return;
    }

    // Validate queue data using model methods
    if (personalQueue.isEmpty()) {
      console.log('⚠️ Personal queue is empty:', queueId);
      if (typeof showNotification === 'function') {
        showNotification('This queue is empty', 'warning');
      }
      return;
    }

    const queueData = personalQueue.queueData;

    // Load the queue data using model methods
    const videos = queueData.queue.map(video => 
      video instanceof VideoModel ? video.toObject() : video
    );
    
    setVideoQueue(videos);
    setCurrentVideoIndex(queueData.currentIndex || 0);
    setIsPlaying(false);

    // Ensure currentVideoIndex is within bounds
    if (getCurrentVideoIndex() >= getVideoQueue().length) {
      setCurrentVideoIndex(0);
    }

    // Update UI (no automatic tab switching)
    updateQueueDisplay();
    updatePlayerControls();
    saveQueueToStorage();

    // Success feedback
    const queueTitle = personalQueue.getTitle();
    const videoCount = personalQueue.getVideoCount();
    console.log(`✅ Personal queue "${queueTitle}" loaded successfully (${videoCount} videos)`);
    
    if (typeof showNotification === 'function') {
      showNotification(`Loaded "${queueTitle}" (${videoCount} videos)`, 'success');
    }

  } catch (error) {
    console.error('❌ Error loading personal queue:', error);
    if (typeof showNotification === 'function') {
      showNotification('Failed to load personal queue. Please try again.', 'error');
    }
  }
}

/**
 * Preload queue data for faster access
 * @param {string} queueId - Queue ID to preload
 */
async function preloadPersonalQueue(queueId) {
  try {
    console.log('⚡ Preloading personal queue:', queueId);
    
    // Load queue data in background
    const personalQueue = await window.personalQueueRepo.load(queueId);
    
    if (personalQueue) {
      console.log('✅ Preloaded personal queue:', queueId);
    }
  } catch (error) {
    console.warn('Could not preload personal queue:', error);
  }
}

/**
 * Refresh personal queues data
 * @param {boolean} showFeedback - Whether to show user feedback
 */
async function refreshPersonalQueues(showFeedback = true) {
  if (showFeedback && typeof showNotification === 'function') {
    showNotification('Refreshing your queues...', 'info');
  }

  // Invalidate cache and reload
  invalidatePersonalQueuesCache();
  await loadPersonalQueues(true);

  if (showFeedback && typeof showNotification === 'function') {
    showNotification('Queues refreshed', 'success');
  }
}

/**
 * Get cache statistics
 * @returns {Object} Cache statistics
 */
function getCacheStats() {
  const now = Date.now();
  const age = personalQueuesCache.timestamp ? now - personalQueuesCache.timestamp : null;
  
  return {
    hasData: !!personalQueuesCache.data,
    userId: personalQueuesCache.userId,
    itemCount: personalQueuesCache.data ? personalQueuesCache.data.length : 0,
    ageMs: age,
    ageSeconds: age ? Math.floor(age / 1000) : null,
    isValid: age ? age < personalQueuesCache.maxAge : false,
    maxAgeSeconds: Math.floor(personalQueuesCache.maxAge / 1000)
  };
}

/**
 * Set cache max age
 * @param {number} maxAgeMs - Max age in milliseconds
 */
function setCacheMaxAge(maxAgeMs) {
  personalQueuesCache.maxAge = maxAgeMs;
  console.log(`📋 Cache max age set to ${Math.floor(maxAgeMs / 1000)} seconds`);
}

/**
 * Enhanced error handling with user feedback
 * @param {Error} error - Error object
 * @param {string} operation - Operation that failed
 * @param {HTMLElement} container - Container to show error in
 */
function handleUIError(error, operation, container = null) {
  console.error(`Error in ${operation}:`, error);
  
  if (container && typeof showErrorState === 'function') {
    showErrorState(container, `Failed to ${operation}`, 'Please check your connection and try again.');
  }
  
  // Show toast notification for user feedback
  if (typeof showNotification === 'function') {
    showNotification(`Failed to ${operation}. Please try again.`, 'error');
  }
}

// Export functions for global access
window.loadPersonalQueues = loadPersonalQueues;
window.loadPersonalQueue = loadPersonalQueue;
window.refreshPersonalQueues = refreshPersonalQueues;
window.invalidatePersonalQueuesCache = invalidatePersonalQueuesCache;
window.preloadPersonalQueue = preloadPersonalQueue;
window.getCacheStats = getCacheStats;
window.setCacheMaxAge = setCacheMaxAge;
window.handleUIError = handleUIError;

console.log('✅ Personal Queues Loading component loaded');
