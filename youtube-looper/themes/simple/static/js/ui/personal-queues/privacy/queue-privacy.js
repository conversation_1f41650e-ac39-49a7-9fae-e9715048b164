// ============================================================================
// PERSONAL QUEUES PRIVACY COMPONENT
// ============================================================================

/**
 * Personal Queues Privacy Component
 * Handles privacy toggle functionality, public/private queue management
 * Manages queue visibility and sharing permissions
 */

/**
 * Initialize privacy toggles for individual queues
 */
function initializeQueuePrivacyToggles() {
  const privacyToggles = document.querySelectorAll('.queue-privacy-toggle');

  privacyToggles.forEach(toggle => {
    // Remove existing listeners to prevent duplicates
    const newToggle = toggle.cloneNode(true);
    toggle.parentNode.replaceChild(newToggle, toggle);
    
    newToggle.addEventListener('change', async function() {
      const queueId = this.dataset.queueId;
      const isPublic = this.checked;

      console.log(`Queue ${queueId} privacy changed: ${isPublic ? 'Public' : 'Private'}`);

      // Show loading state on toggle
      this.disabled = true;
      const queueCard = this.closest('.queue-card');
      if (queueCard) {
        queueCard.classList.add('updating');
      }

      try {
        await updateQueuePrivacy(queueId, isPublic);

        // Invalidate cache since queue data has changed
        if (typeof invalidatePersonalQueuesCache === 'function') {
          invalidatePersonalQueuesCache();
        }

        // Update the privacy badge
        const privacyBadge = queueCard.querySelector('.privacy-badge');
        if (privacyBadge) {
          privacyBadge.textContent = isPublic ? 'Public' : 'Private';
          privacyBadge.className = `privacy-badge ${isPublic ? 'public' : 'private'}`;
        }

        console.log(`✅ Queue ${isPublic ? 'made public' : 'made private'}`);

        // Update the specific queue card without refreshing the entire list
        updateQueueCardPrivacy(queueId, isPublic);

        // Show success feedback
        if (typeof showNotification === 'function') {
          showNotification(
            `Queue ${isPublic ? 'made public' : 'made private'} successfully`, 
            'success'
          );
        }

      } catch (error) {
        console.error('❌ Error updating queue privacy:', error);
        
        // Revert the toggle
        this.checked = !isPublic;
        
        // Show error feedback
        if (typeof showNotification === 'function') {
          showNotification('Failed to update queue privacy. Please try again.', 'error');
        }
      } finally {
        // Remove loading state
        this.disabled = false;
        if (queueCard) {
          queueCard.classList.remove('updating');
        }
      }
    });
  });
}

/**
 * Update queue privacy setting in Firebase
 * @param {string} queueId - Queue ID
 * @param {boolean} isPublic - Whether queue should be public
 */
async function updateQueuePrivacy(queueId, isPublic) {
  const userId = await getCurrentUserId();
  if (!userId) {
    throw new Error('User not authenticated');
  }

  try {
    // Use the new repository to load the personal queue
    const personalQueue = await window.personalQueueRepo.load(queueId);

    if (!personalQueue) {
      throw new Error('Queue not found');
    }

    if (isPublic) {
      // If making public, add to public queues collection
      console.log('📤 Making queue public:', queueId);

      // Create metadata from queue data
      const metadata = QueueMetadataModel.fromQueueData(personalQueue.queueData);

      // Create public queue model
      const publicQueue = new PublicQueueModel({
        [PublicQueueModel.FIELDS.ID]: queueId,
        [PublicQueueModel.FIELDS.QUEUE_DATA]: personalQueue.queueData.toObject(),
        [PublicQueueModel.FIELDS.METADATA]: metadata.toObject()
      });

      // Save to public collection using repository
      await window.publicQueueRepo.save(publicQueue);

      // Update personal queue to mark as public
      personalQueue.isPublic = true;
      await window.personalQueueRepo.save(personalQueue);

    } else {
      // If making private, remove from public queues collection
      console.log('🔒 Making queue private:', queueId);
      
      await window.publicQueueRepo.delete(queueId);

      // Update personal queue to mark as private
      personalQueue.isPublic = false;
      await window.personalQueueRepo.save(personalQueue);
    }
  } catch (error) {
    console.error('❌ Error updating queue privacy:', error);
    throw error;
  }
}

/**
 * Update a specific queue card's privacy display without refreshing the entire list
 * @param {string} queueId - ID of the queue to update
 * @param {boolean} isPublic - New privacy status
 */
function updateQueueCardPrivacy(queueId, isPublic) {
  const queueCard = document.querySelector(`.queue-card[data-queue-id="${queueId}"]`);
  if (!queueCard) return;

  // Update privacy badge
  const privacyBadge = queueCard.querySelector('.privacy-badge');
  if (privacyBadge) {
    privacyBadge.textContent = isPublic ? 'Public' : 'Private';
    privacyBadge.className = `privacy-badge ${isPublic ? 'public' : 'private'}`;
  }

  // Update privacy toggle checkbox
  const privacyToggle = queueCard.querySelector('.queue-privacy-toggle');
  if (privacyToggle) {
    privacyToggle.checked = isPublic;
  }

  // Add or remove queue link section
  const queueInfo = queueCard.querySelector('.queue-info');
  if (!queueInfo) return;

  // Remove existing queue link section if it exists
  const existingLinkSection = queueInfo.querySelector('.queue-link-section');
  if (existingLinkSection) {
    existingLinkSection.remove();
  }

  // Add queue link section if making public
  if (isPublic) {
    const queueLinkHTML = `
      <div class="queue-link-section">
        <div class="queue-link-header">
          <span class="queue-link-label">Share Link</span>
          <button class="copy-queue-link-btn" data-queue-id="${queueId}" title="Copy Queue Link">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
            </svg>
          </button>
        </div>
        <div class="queue-link-display">
          <span class="queue-link-text">${window.location.origin}${window.location.pathname}?q=${queueId}</span>
        </div>
      </div>
    `;

    // Insert the queue link section before the queue actions
    const queueActions = queueCard.querySelector('.queue-actions');
    if (queueActions) {
      queueInfo.insertAdjacentHTML('beforeend', queueLinkHTML);
    }

    // Add event listener to the new copy button
    const newCopyButton = queueInfo.querySelector('.copy-queue-link-btn');
    if (newCopyButton && typeof addCopyButtonEventListener === 'function') {
      addCopyButtonEventListener(newCopyButton);
    }
  }
}

/**
 * Get queue privacy status
 * @param {string} queueId - Queue ID
 * @returns {boolean|null} Privacy status or null if not found
 */
function getQueuePrivacyStatus(queueId) {
  const queueCard = document.querySelector(`.queue-card[data-queue-id="${queueId}"]`);
  if (!queueCard) return null;

  const privacyToggle = queueCard.querySelector('.queue-privacy-toggle');
  return privacyToggle ? privacyToggle.checked : null;
}

/**
 * Toggle queue privacy
 * @param {string} queueId - Queue ID
 * @returns {Promise<boolean>} New privacy status
 */
async function toggleQueuePrivacy(queueId) {
  const currentStatus = getQueuePrivacyStatus(queueId);
  if (currentStatus === null) {
    throw new Error('Queue not found');
  }

  const newStatus = !currentStatus;
  await updateQueuePrivacy(queueId, newStatus);
  updateQueueCardPrivacy(queueId, newStatus);
  
  return newStatus;
}

/**
 * Batch update privacy for multiple queues
 * @param {Array} queueIds - Array of queue IDs
 * @param {boolean} isPublic - Privacy status to set
 * @returns {Promise<Object>} Results with success and failed arrays
 */
async function batchUpdateQueuePrivacy(queueIds, isPublic) {
  const results = {
    success: [],
    failed: []
  };

  for (const queueId of queueIds) {
    try {
      await updateQueuePrivacy(queueId, isPublic);
      updateQueueCardPrivacy(queueId, isPublic);
      results.success.push(queueId);
    } catch (error) {
      console.error(`Failed to update privacy for queue ${queueId}:`, error);
      results.failed.push({ queueId, error: error.message });
    }
  }

  return results;
}

/**
 * Get all public queue IDs from current display
 * @returns {Array<string>} Array of public queue IDs
 */
function getPublicQueueIds() {
  const publicToggles = document.querySelectorAll('.queue-privacy-toggle:checked');
  return Array.from(publicToggles).map(toggle => toggle.dataset.queueId);
}

/**
 * Get all private queue IDs from current display
 * @returns {Array<string>} Array of private queue IDs
 */
function getPrivateQueueIds() {
  const privateToggles = document.querySelectorAll('.queue-privacy-toggle:not(:checked)');
  return Array.from(privateToggles).map(toggle => toggle.dataset.queueId);
}

/**
 * Initialize privacy management
 */
function initializeQueuePrivacyManagement() {
  // Set up privacy toggles
  initializeQueuePrivacyToggles();
  
  console.log('🔒 Queue privacy management initialized');
}

// Export functions for global access
window.initializeQueuePrivacyToggles = initializeQueuePrivacyToggles;
window.updateQueuePrivacy = updateQueuePrivacy;
window.updateQueueCardPrivacy = updateQueueCardPrivacy;
window.getQueuePrivacyStatus = getQueuePrivacyStatus;
window.toggleQueuePrivacy = toggleQueuePrivacy;
window.batchUpdateQueuePrivacy = batchUpdateQueuePrivacy;
window.getPublicQueueIds = getPublicQueueIds;
window.getPrivateQueueIds = getPrivateQueueIds;

console.log('✅ Personal Queues Privacy component loaded');
