// ============================================================================
// PERSONAL QUEUES ACTIONS COMPONENT
// ============================================================================

/**
 * Personal Queues Actions Component
 * Handles queue actions like delete, duplicate, export, and other operations
 * Manages user-initiated actions on personal queues
 */

/**
 * Delete a personal queue with enhanced feedback
 * @param {string} queueId - ID of the personal queue to delete
 */
async function deletePersonalQueue(queueId) {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      console.log('❌ User not signed in');
      if (typeof showNotification === 'function') {
        showNotification('Please sign in to delete queues', 'warning');
      }
      return;
    }

    // Load queue info for better confirmation dialog
    let queueTitle = 'this queue';
    try {
      const personalQueue = await window.personalQueueRepo.load(queueId);
      if (personalQueue) {
        queueTitle = `"${personalQueue.getTitle()}"`;
      }
    } catch (error) {
      console.warn('Could not load queue info for confirmation:', error);
    }

    // Enhanced confirmation dialog
    const confirmMessage = `Are you sure you want to delete ${queueTitle}?\n\nThis action cannot be undone and will permanently remove all videos from this queue.`;
    if (!confirm(confirmMessage)) {
      return;
    }

    console.log('🗑️ Deleting queue:', queueId);

    // Show loading feedback
    if (typeof showNotification === 'function') {
      showNotification('Deleting queue...', 'info');
    }

    // Use the new repository to delete the queue
    await window.personalQueueRepo.delete(queueId);

    // Invalidate cache since queue data has changed
    if (typeof invalidatePersonalQueuesCache === 'function') {
      invalidatePersonalQueuesCache();
    }

    console.log('✅ Queue deleted successfully');

    // Success feedback
    if (typeof showNotification === 'function') {
      showNotification(`Queue ${queueTitle} deleted successfully`, 'success');
    }

    // Reload the personal queues list with force refresh
    if (getCurrentView() === 'personal') {
      if (typeof loadPersonalQueues === 'function') {
        loadPersonalQueues(true);
      }
    }

  } catch (error) {
    console.error('❌ Error deleting personal queue:', error);
    if (typeof showNotification === 'function') {
      showNotification('Failed to delete queue. Please try again.', 'error');
    }
  }
}

/**
 * Duplicate a personal queue
 * @param {string} queueId - ID of the queue to duplicate
 */
async function duplicatePersonalQueue(queueId) {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      console.log('❌ User not signed in');
      if (typeof showNotification === 'function') {
        showNotification('Please sign in to duplicate queues', 'warning');
      }
      return;
    }

    console.log('📋 Duplicating queue:', queueId);

    // Show loading feedback
    if (typeof showNotification === 'function') {
      showNotification('Duplicating queue...', 'info');
    }

    // Load the original queue
    const originalQueue = await window.personalQueueRepo.load(queueId);
    if (!originalQueue) {
      throw new Error('Original queue not found');
    }

    // Create a new queue with copied data
    const duplicatedQueue = PersonalQueueModel.fromCurrentState(
      userId,
      `${originalQueue.getTitle()} (Copy)`
    );

    // Copy the queue data
    duplicatedQueue.queueData = originalQueue.queueData;

    // Save the duplicated queue
    await window.personalQueueRepo.save(duplicatedQueue);

    // Invalidate cache
    if (typeof invalidatePersonalQueuesCache === 'function') {
      invalidatePersonalQueuesCache();
    }

    console.log('✅ Queue duplicated successfully');

    // Success feedback
    if (typeof showNotification === 'function') {
      showNotification(`Queue duplicated as "${duplicatedQueue.getTitle()}"`, 'success');
    }

    // Refresh the list
    if (getCurrentView() === 'personal') {
      if (typeof loadPersonalQueues === 'function') {
        loadPersonalQueues(true);
      }
    }

  } catch (error) {
    console.error('❌ Error duplicating queue:', error);
    if (typeof showNotification === 'function') {
      showNotification('Failed to duplicate queue. Please try again.', 'error');
    }
  }
}

/**
 * Rename a personal queue
 * @param {string} queueId - ID of the queue to rename
 */
async function renamePersonalQueue(queueId) {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      console.log('❌ User not signed in');
      if (typeof showNotification === 'function') {
        showNotification('Please sign in to rename queues', 'warning');
      }
      return;
    }

    // Load the queue
    const personalQueue = await window.personalQueueRepo.load(queueId);
    if (!personalQueue) {
      throw new Error('Queue not found');
    }

    const currentTitle = personalQueue.getTitle();
    const newTitle = prompt('Enter new queue name:', currentTitle);

    if (!newTitle || newTitle.trim() === '' || newTitle === currentTitle) {
      return; // User cancelled or no change
    }

    console.log('✏️ Renaming queue:', queueId, 'to:', newTitle);

    // Show loading feedback
    if (typeof showNotification === 'function') {
      showNotification('Renaming queue...', 'info');
    }

    // Update the queue title
    personalQueue.queueData.title = newTitle.trim();
    personalQueue.updateLastModified();

    // Save the updated queue
    await window.personalQueueRepo.save(personalQueue);

    // Invalidate cache
    if (typeof invalidatePersonalQueuesCache === 'function') {
      invalidatePersonalQueuesCache();
    }

    // Update the queue card in place
    if (typeof updateQueueCard === 'function') {
      updateQueueCard(queueId, { title: newTitle.trim() });
    }

    console.log('✅ Queue renamed successfully');

    // Success feedback
    if (typeof showNotification === 'function') {
      showNotification(`Queue renamed to "${newTitle.trim()}"`, 'success');
    }

  } catch (error) {
    console.error('❌ Error renaming queue:', error);
    if (typeof showNotification === 'function') {
      showNotification('Failed to rename queue. Please try again.', 'error');
    }
  }
}

/**
 * Export queue data as JSON
 * @param {string} queueId - ID of the queue to export
 */
async function exportPersonalQueue(queueId) {
  try {
    console.log('📤 Exporting queue:', queueId);

    // Load the queue
    const personalQueue = await window.personalQueueRepo.load(queueId);
    if (!personalQueue) {
      throw new Error('Queue not found');
    }

    // Prepare export data
    const exportData = {
      title: personalQueue.getTitle(),
      createdAt: personalQueue.getCreatedAt(),
      lastModified: personalQueue.getLastModified(),
      videoCount: personalQueue.getVideoCount(),
      videos: personalQueue.queueData.queue.map(video => ({
        id: video.id,
        title: video.title,
        thumbnail: video.thumbnail,
        duration: video.duration,
        channel: video.channel,
        url: `https://www.youtube.com/watch?v=${video.id}`
      })),
      exportedAt: new Date().toISOString(),
      exportedBy: 'YouTube Looper'
    };

    // Create and download file
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `${personalQueue.getTitle().replace(/[^a-z0-9]/gi, '_').toLowerCase()}_queue.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    console.log('✅ Queue exported successfully');

    // Success feedback
    if (typeof showNotification === 'function') {
      showNotification(`Queue "${personalQueue.getTitle()}" exported successfully`, 'success');
    }

  } catch (error) {
    console.error('❌ Error exporting queue:', error);
    if (typeof showNotification === 'function') {
      showNotification('Failed to export queue. Please try again.', 'error');
    }
  }
}

/**
 * Get queue statistics
 * @param {string} queueId - ID of the queue
 * @returns {Promise<Object|null>} Queue statistics or null if not found
 */
async function getPersonalQueueStats(queueId) {
  try {
    const personalQueue = await window.personalQueueRepo.load(queueId);
    if (!personalQueue) {
      return null;
    }

    return {
      id: queueId,
      title: personalQueue.getTitle(),
      videoCount: personalQueue.getVideoCount(),
      totalDuration: personalQueue.getTotalDuration(),
      formattedDuration: personalQueue.getFormattedTotalDuration(),
      createdAt: personalQueue.getCreatedAt(),
      lastModified: personalQueue.getLastModified(),
      isPublic: personalQueue.isPublic,
      isEmpty: personalQueue.isEmpty()
    };
  } catch (error) {
    console.error('Error getting queue stats:', error);
    return null;
  }
}

/**
 * Batch delete multiple queues
 * @param {Array<string>} queueIds - Array of queue IDs to delete
 * @returns {Promise<Object>} Results with success and failed arrays
 */
async function batchDeletePersonalQueues(queueIds) {
  const results = {
    success: [],
    failed: []
  };

  if (!queueIds || queueIds.length === 0) {
    return results;
  }

  const confirmMessage = `Are you sure you want to delete ${queueIds.length} queue(s)?\n\nThis action cannot be undone.`;
  if (!confirm(confirmMessage)) {
    return results;
  }

  // Show loading feedback
  if (typeof showNotification === 'function') {
    showNotification(`Deleting ${queueIds.length} queues...`, 'info');
  }

  for (const queueId of queueIds) {
    try {
      await window.personalQueueRepo.delete(queueId);
      results.success.push(queueId);
    } catch (error) {
      console.error(`Failed to delete queue ${queueId}:`, error);
      results.failed.push({ queueId, error: error.message });
    }
  }

  // Invalidate cache
  if (typeof invalidatePersonalQueuesCache === 'function') {
    invalidatePersonalQueuesCache();
  }

  // Show results
  if (typeof showNotification === 'function') {
    if (results.failed.length === 0) {
      showNotification(`Successfully deleted ${results.success.length} queue(s)`, 'success');
    } else {
      showNotification(
        `Deleted ${results.success.length} queue(s), ${results.failed.length} failed`, 
        'warning'
      );
    }
  }

  // Refresh the list
  if (getCurrentView() === 'personal') {
    if (typeof loadPersonalQueues === 'function') {
      loadPersonalQueues(true);
    }
  }

  return results;
}

/**
 * Create a new personal queue from current playing queue
 * @param {string} title - Title for the new queue
 */
async function saveCurrentQueueAsPersonal(title) {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      console.log('❌ User not signed in');
      if (typeof showNotification === 'function') {
        showNotification('Please sign in to save queues', 'warning');
      }
      return;
    }

    const videoQueue = getVideoQueue();
    if (!videoQueue || videoQueue.length === 0) {
      if (typeof showNotification === 'function') {
        showNotification('No videos in current queue to save', 'warning');
      }
      return;
    }

    console.log('💾 Saving current queue as personal queue:', title);

    // Show loading feedback
    if (typeof showNotification === 'function') {
      showNotification('Saving queue...', 'info');
    }

    // Create personal queue from current state
    const personalQueue = PersonalQueueModel.fromCurrentState(userId, title);

    // Save the queue
    await window.personalQueueRepo.save(personalQueue);

    // Invalidate cache
    if (typeof invalidatePersonalQueuesCache === 'function') {
      invalidatePersonalQueuesCache();
    }

    console.log('✅ Current queue saved as personal queue');

    // Success feedback
    if (typeof showNotification === 'function') {
      showNotification(`Queue "${title}" saved successfully`, 'success');
    }

    // Refresh the list if on personal view
    if (getCurrentView() === 'personal') {
      if (typeof loadPersonalQueues === 'function') {
        loadPersonalQueues(true);
      }
    }

  } catch (error) {
    console.error('❌ Error saving current queue:', error);
    if (typeof showNotification === 'function') {
      showNotification('Failed to save queue. Please try again.', 'error');
    }
  }
}

// Export functions for global access
window.deletePersonalQueue = deletePersonalQueue;
window.duplicatePersonalQueue = duplicatePersonalQueue;
window.renamePersonalQueue = renamePersonalQueue;
window.exportPersonalQueue = exportPersonalQueue;
window.getPersonalQueueStats = getPersonalQueueStats;
window.batchDeletePersonalQueues = batchDeletePersonalQueues;
window.saveCurrentQueueAsPersonal = saveCurrentQueueAsPersonal;

console.log('✅ Personal Queues Actions component loaded');
