# Queue Management Components

This directory contains the modular queue management system, broken down into feature-based components for better maintainability and organization.

## Directory Structure

```
queue-management/
├── display/
│   └── queue-display.js           # Queue visualization and rendering
├── controls/
│   └── queue-controls.js          # Play/pause/next/previous controls
├── items/
│   └── queue-items.js             # Individual queue item interactions
├── actions/
│   └── queue-actions.js           # Add/remove/clear operations
├── scrolling/
│   └── queue-scrolling.js         # Scroll controls and navigation
├── draft/
│   └── draft-queue.js             # Draft queue and creation mode
├── sharing/
│   └── queue-sharing.js           # Queue sharing and link management
├── index.js                       # Main controller and API
└── README.md                      # This file
```

## Component Overview

### 1. Queue Display (`display/queue-display.js`)
**Purpose**: Handles queue visualization, rendering, and UI updates
**Key Functions**:
- `updateQueueDisplay()` - Main rendering function
- `renderQueueItems()` - Render individual queue items
- `updateQueueActionButtons()` - Update button states
- `getQueueStats()` - Calculate queue statistics

**Responsibilities**:
- Queue item HTML generation
- Empty state display
- Action button visibility
- Statistics calculation
- Link display management

### 2. Queue Controls (`controls/queue-controls.js`)
**Purpose**: Manages playback controls and queue navigation
**Key Functions**:
- `playQueue()` - Play/pause functionality
- `nextVideo()` / `previousVideo()` - Navigation
- `jumpToVideo(index)` - Direct video selection
- `handleVideoEnd()` - Auto-advance logic

**Responsibilities**:
- Playback state management
- Video navigation
- Shuffle/repeat modes
- Keyboard shortcuts
- Auto-advance logic

### 3. Queue Items (`items/queue-items.js`)
**Purpose**: Handles individual queue item interactions
**Key Functions**:
- `addQueueItemEventListeners()` - Set up item interactions
- `handleQueueItemPlay()` - Item play/pause
- `handleQueueItemCopyUrl()` - Copy video URLs
- `highlightQueueItem()` - Visual feedback

**Responsibilities**:
- Item click handling
- Copy URL functionality
- Visual feedback
- Item state updates
- Touch/mobile interactions

### 4. Queue Actions (`actions/queue-actions.js`)
**Purpose**: Manages queue manipulation operations
**Key Functions**:
- `addVideoToQueue()` - Add videos
- `removeVideoFromQueue()` - Remove videos
- `clearQueue()` - Clear entire queue
- `moveVideoInQueue()` - Reorder videos

**Responsibilities**:
- Queue state modifications
- Video addition/removal
- Queue reordering
- Bulk operations
- State persistence

### 5. Queue Scrolling (`scrolling/queue-scrolling.js`)
**Purpose**: Handles queue container scrolling and navigation
**Key Functions**:
- `scrollQueueUp()` / `scrollQueueDown()` - Programmatic scrolling
- `scrollToQueueItem()` - Scroll to specific item
- `autoScrollToCurrentItem()` - Auto-scroll to playing item

**Responsibilities**:
- Smooth scrolling
- Keyboard navigation
- Touch/wheel events
- Programmatic scroll control

**Note**: Visual scroll buttons have been removed. Scrolling is available via keyboard navigation and programmatic API calls.

### 6. Draft Queue (`draft/draft-queue.js`)
**Purpose**: Manages queue creation mode and draft queue functionality
**Key Functions**:
- `getDraftQueue()` / `setDraftQueue()` - Draft queue state management
- `addVideoToDraftQueue()` / `removeVideoFromDraftQueue()` - Draft queue manipulation
- `enterQueueCreationMode()` / `exitQueueCreationMode()` - Creation mode management
- `isInQueueCreationMode()` - Check creation mode status

**Responsibilities**:
- Draft queue state management
- Queue creation mode UI
- Draft queue persistence
- Creation mode coordination
- Draft queue validation

### 7. Queue Sharing (`sharing/queue-sharing.js`)
**Purpose**: Handles queue sharing functionality and link management
**Key Functions**:
- `shareCurrentQueue()` - Share queue publicly
- `copyQueueLink()` - Copy shareable links
- `generateShareableLink()` - Generate queue URLs
- `updateSharingUI()` - Update sharing interface

**Responsibilities**:
- Public queue sharing
- Link generation and copying
- Sharing UI management
- Auto-update shared queues
- Sharing status tracking

### 6. Main Controller (`index.js`)
**Purpose**: Coordinates all components and provides unified API
**Key Functions**:
- `initializeQueueManagement()` - Initialize all components
- `refreshQueueManagement()` - Refresh entire interface
- `QueueManagementAPI` - Unified API object
- Event coordination and state management

## API Usage

### Unified Queue Management API

```javascript
// Access the unified API
const qm = window.QueueManagement;

// Get current state
const state = qm.getState();
console.log(state.queueLength); // Number of videos
console.log(state.currentVideo); // Currently playing video

// Control playback
qm.play();           // Play/pause
qm.next();           // Next video
qm.previous();       // Previous video
qm.jumpTo(5);        // Jump to video at index 5

// Manage queue
qm.addVideo(videoObj, true);  // Add video and play immediately
qm.removeVideo(3);            // Remove video at index 3
qm.clear();                   // Clear entire queue
qm.shuffle();                 // Shuffle queue order

// Control scrolling
qm.scrollUp();               // Scroll up
qm.scrollDown();             // Scroll down
qm.scrollToItem(2);          // Scroll to video at index 2
qm.scrollToCurrent();        // Scroll to currently playing video

// Update interface
qm.refresh();                // Refresh entire interface
qm.updateDisplay();          // Update display only
```

### Individual Component Functions

```javascript
// Display functions
updateQueueDisplay();
refreshQueueDisplay();
getQueueStats();

// Control functions
playQueue();
nextVideo();
previousVideo();
jumpToVideo(index);

// Action functions
addVideoToQueue(video, playImmediately);
removeVideoFromQueue(index);
clearQueue();
shuffleQueue();

// Scrolling functions
scrollQueueUp();
scrollQueueDown();
scrollToQueueItem(index);
autoScrollToCurrentItem();
```

## Event System

### Queue State Changes
```javascript
// Listen for queue state changes
document.addEventListener('queueStateChanged', (event) => {
  const { type, data, state } = event.detail;
  console.log('Queue changed:', type, data);
  console.log('New state:', state);
});

// Emit queue state changes
emitQueueStateChange('video_added', { videoId: 'abc123' });
```

### Player State Changes
```javascript
// Listen for player state changes
document.addEventListener('playerStateChanged', (event) => {
  const { state, data } = event.detail;
  console.log('Player state:', state);
});
```

## Component Dependencies

### External Dependencies
- `getVideoQueue()` / `setVideoQueue()` - Queue state management
- `getCurrentVideoIndex()` / `setCurrentVideoIndex()` - Current video tracking
- `getIsPlaying()` / `setIsPlaying()` - Playback state
- `getPlayer()` / `createPlayer()` - YouTube player management
- `updatePlayerControls()` - Player UI updates
- `saveQueueToStorage()` - Persistence

### Internal Dependencies
```
index.js
├── display/queue-display.js
├── controls/queue-controls.js
├── items/queue-items.js
├── actions/queue-actions.js
└── scrolling/queue-scrolling.js
```

## Loading Order

Components must be loaded in this order:

1. **Individual Components** (can be loaded in parallel):
   - `display/queue-display.js`
   - `controls/queue-controls.js`
   - `items/queue-items.js`
   - `actions/queue-actions.js`
   - `scrolling/queue-scrolling.js`

2. **Main Controller**:
   - `index.js` (coordinates all components)

## HTML Integration

### Required HTML Elements
```html
<!-- Queue container -->
<div id="queue-container"></div>

<!-- Queue controls -->
<button id="play-queue-btn"></button>
<button id="clear-queue-btn"></button>
<button id="share-queue-btn"></button>

<!-- Statistics -->
<div id="queue-stats"></div>
<div id="queue-count"></div>

<!-- Queue link -->
<div id="queue-link-section">
  <span id="queue-link-text"></span>
</div>
```

**Note**: Scroll control buttons (`queue-scroll-up`, `queue-scroll-down`, `queue-scroll-progress`) have been removed. Scrolling is handled via keyboard navigation and programmatic API calls.

## Performance Considerations

### Optimizations Implemented
- **Debounced scroll events** to prevent excessive updates
- **Lazy loading** for queue item images
- **Event delegation** for dynamic content
- **Smooth scrolling** with CSS transitions
- **Efficient DOM updates** with minimal reflows

### Memory Management
- Event listeners are properly cleaned up
- No memory leaks from closures
- Efficient DOM manipulation
- Minimal global state

## Future Enhancements

### Planned Features
1. **Drag & Drop Reordering** - Visual queue reordering
2. **Virtual Scrolling** - Handle very large queues efficiently
3. **Queue Search** - Search within current queue
4. **Queue Filters** - Filter by duration, channel, etc.
5. **Queue Analytics** - Track usage patterns
6. **Queue Themes** - Customizable visual themes

### Architecture Improvements
1. **Component Communication** - Better inter-component messaging
2. **State Management** - Centralized state with reducers
3. **Testing Framework** - Unit tests for each component
4. **Performance Monitoring** - Real-time performance metrics

## Migration Notes

This modular system replaces the monolithic `queue-management-controller.js` file with the following benefits:

- ✅ **Better Organization** - Logical separation of concerns
- ✅ **Easier Maintenance** - Smaller, focused files
- ✅ **Improved Testing** - Components can be tested independently
- ✅ **Better Performance** - Optimized loading and execution
- ✅ **Enhanced Reusability** - Components can be reused elsewhere
- ✅ **Clearer Dependencies** - Explicit component relationships

All existing functionality is preserved while providing a much more maintainable and scalable architecture.
