// ============================================================================
// QUEUE SHARING COMPONENT
// ============================================================================

/**
 * Queue Sharing Component
 * Handles queue sharing functionality, link generation, and copy operations
 * Manages public queue sharing and link management features
 */

/**
 * Share current queue
 * @param {string|null} queueId - Optional existing queue ID
 * @returns {Promise<string|null>} Queue ID if successful
 */
async function shareCurrentQueue(queueId = null) {
  try {
    const videoQueue = getVideoQueue();
    
    if (videoQueue.length === 0) {
      console.log('Cannot share empty queue');
      
      if (typeof showNotification === 'function') {
        showNotification('Cannot share an empty queue', 'warning');
      }
      
      return null;
    }

    // Show loading state
    if (typeof showNotification === 'function') {
      showNotification('Sharing queue...', 'info');
    }

    // Use Firebase service to save queue
    const savedQueueId = await saveQueueToFirebase(queueId, false);
    
    if (savedQueueId) {
      // Generate shareable link
      const shareableLink = generateShareableLink(savedQueueId);
      
      // Copy to clipboard
      const copySuccess = await copyToClipboard(shareableLink);
      
      if (copySuccess) {
        console.log('✅ Queue shared and link copied to clipboard');
        
        if (typeof showNotification === 'function') {
          showNotification('Queue shared! Link copied to clipboard', 'success');
        }
      } else {
        console.log('✅ Queue shared successfully');
        
        if (typeof showNotification === 'function') {
          showNotification('Queue shared successfully', 'success');
        }
        
        // Show link in prompt as fallback
        prompt('Queue shared! Copy this link:', shareableLink);
      }

      // Update UI to show share link
      updateShareLinkDisplay(savedQueueId, shareableLink);
      
      return savedQueueId;
    } else {
      console.log('❌ Failed to share queue');
      
      if (typeof showNotification === 'function') {
        showNotification('Failed to share queue. Please try again.', 'error');
      }
      
      return null;
    }
  } catch (error) {
    console.error('❌ Error sharing queue:', error);
    
    if (typeof showNotification === 'function') {
      showNotification('Failed to share queue. Please try again.', 'error');
    }
    
    return null;
  }
}

/**
 * Generate shareable link for queue
 * @param {string} queueId - Queue ID
 * @returns {string} Shareable link
 */
function generateShareableLink(queueId) {
  const baseUrl = window.location.origin;
  const pathname = window.location.pathname;
  return `${baseUrl}${pathname}?q=${queueId}`;
}

/**
 * Copy text to clipboard
 * @param {string} text - Text to copy
 * @returns {Promise<boolean>} True if successful
 */
async function copyToClipboard(text) {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (error) {
    console.warn('Could not copy to clipboard:', error);
    return false;
  }
}

/**
 * Copy queue link to clipboard
 * @param {string} queueId - Queue ID
 * @returns {Promise<boolean>} True if successful
 */
async function copyQueueLink(queueId) {
  const shareableLink = generateShareableLink(queueId);
  const success = await copyToClipboard(shareableLink);
  
  if (success) {
    console.log('📋 Queue link copied to clipboard');
    
    if (typeof showNotification === 'function') {
      showNotification('Queue link copied to clipboard', 'success');
    }
    
    // Show visual feedback
    showCopyLinkFeedback(queueId);
  } else {
    console.log('❌ Failed to copy queue link');
    
    if (typeof showNotification === 'function') {
      showNotification('Failed to copy link', 'error');
    }
    
    // Show link in prompt as fallback
    prompt('Copy this queue link:', shareableLink);
  }
  
  return success;
}

/**
 * Show visual feedback for copy link action
 * @param {string} queueId - Queue ID
 */
function showCopyLinkFeedback(queueId) {
  const copyBtn = document.querySelector(`[data-queue-id="${queueId}"] .copy-queue-link-btn`);
  
  if (copyBtn) {
    const originalContent = copyBtn.innerHTML;
    copyBtn.innerHTML = '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg>';
    copyBtn.classList.add('copied');
    
    setTimeout(() => {
      copyBtn.innerHTML = originalContent;
      copyBtn.classList.remove('copied');
    }, 2000);
  }
}

/**
 * Update share link display in UI
 * @param {string} queueId - Queue ID
 * @param {string} shareableLink - Shareable link
 */
function updateShareLinkDisplay(queueId, shareableLink) {
  const queueLinkSection = document.getElementById('queue-link-section');
  const queueLinkText = document.getElementById('queue-link-text');
  
  if (queueLinkSection && queueLinkText) {
    queueLinkText.textContent = shareableLink;
    queueLinkSection.style.display = 'block';
    
    // Store queue ID for future reference
    if (typeof setCurrentQueueId === 'function') {
      setCurrentQueueId(queueId);
    }
  }
}

/**
 * Hide share link display
 */
function hideShareLinkDisplay() {
  const queueLinkSection = document.getElementById('queue-link-section');
  
  if (queueLinkSection) {
    queueLinkSection.style.display = 'none';
  }
}

/**
 * Initialize queue link copy buttons
 */
function initializeQueueLinkCopyButtons() {
  document.addEventListener('click', function(e) {
    const copyBtn = e.target.closest('.copy-queue-link-btn');
    
    if (copyBtn) {
      e.preventDefault();
      e.stopPropagation();
      
      const queueId = copyBtn.getAttribute('data-queue-id');
      if (queueId) {
        copyQueueLink(queueId);
      }
    }
  });
}

/**
 * Get current queue sharing status
 * @returns {Object} Sharing status information
 */
function getQueueSharingStatus() {
  const videoQueue = getVideoQueue();
  const currentQueueId = getCurrentQueueId ? getCurrentQueueId() : null;
  const isShared = !!(currentQueueId && videoQueue.length > 0);
  
  return {
    isShared,
    queueId: currentQueueId,
    shareableLink: isShared ? generateShareableLink(currentQueueId) : null,
    canShare: videoQueue.length > 0,
    queueLength: videoQueue.length
  };
}

/**
 * Update sharing UI based on current state
 */
function updateSharingUI() {
  const status = getQueueSharingStatus();
  const shareBtn = document.getElementById('share-queue-btn');
  const queueLinkSection = document.getElementById('queue-link-section');
  
  // Update share button
  if (shareBtn) {
    shareBtn.style.display = status.canShare ? 'block' : 'none';
    
    if (status.isShared) {
      shareBtn.textContent = 'Update Share';
      shareBtn.title = 'Update shared queue';
    } else {
      shareBtn.textContent = 'Share Queue';
      shareBtn.title = 'Share this queue publicly';
    }
  }
  
  // Update link section
  if (queueLinkSection) {
    if (status.isShared) {
      updateShareLinkDisplay(status.queueId, status.shareableLink);
    } else {
      hideShareLinkDisplay();
    }
  }
}

/**
 * Handle share button click
 */
function handleShareButtonClick() {
  const shareBtn = document.getElementById('share-queue-btn');
  
  if (shareBtn) {
    shareBtn.addEventListener('click', async function(e) {
      e.preventDefault();
      
      // Disable button during operation
      const originalText = shareBtn.textContent;
      shareBtn.disabled = true;
      shareBtn.textContent = 'Sharing...';
      
      try {
        const currentQueueId = getCurrentQueueId ? getCurrentQueueId() : null;
        await shareCurrentQueue(currentQueueId);
      } finally {
        // Re-enable button
        shareBtn.disabled = false;
        shareBtn.textContent = originalText;
        
        // Update UI
        updateSharingUI();
      }
    });
  }
}

/**
 * Auto-update shared queue if it exists
 */
async function autoUpdateSharedQueue() {
  const currentQueueId = getCurrentQueueId ? getCurrentQueueId() : null;
  
  if (currentQueueId && typeof saveQueueToFirebase === 'function') {
    try {
      await saveQueueToFirebase(currentQueueId, true); // isAutoUpdate = true
      console.log('🔄 Auto-updated shared queue');
    } catch (error) {
      console.warn('Could not auto-update shared queue:', error);
    }
  }
}

/**
 * Initialize queue sharing component
 */
function initializeQueueSharing() {
  // Initialize copy button handlers
  initializeQueueLinkCopyButtons();
  
  // Initialize share button handler
  handleShareButtonClick();
  
  // Initial UI update
  updateSharingUI();
  
  console.log('🔗 Queue sharing component initialized');
}

/**
 * Get current queue ID
 * @returns {string|null} Current queue ID
 */
function getCurrentQueueId() {
  try {
    return sessionStorage.getItem('currentQueueId');
  } catch (error) {
    console.warn('Could not get current queue ID:', error);
    return null;
  }
}

/**
 * Set current queue ID
 * @param {string|null} queueId - Queue ID to set
 */
function setCurrentQueueId(queueId) {
  try {
    if (queueId) {
      sessionStorage.setItem('currentQueueId', queueId);
    } else {
      sessionStorage.removeItem('currentQueueId');
    }
  } catch (error) {
    console.warn('Could not set current queue ID:', error);
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeQueueSharing);
} else {
  initializeQueueSharing();
}

// Export functions for global access
window.shareCurrentQueue = shareCurrentQueue;
window.copyQueueLink = copyQueueLink;
window.generateShareableLink = generateShareableLink;
window.getQueueSharingStatus = getQueueSharingStatus;
window.updateSharingUI = updateSharingUI;
window.autoUpdateSharedQueue = autoUpdateSharedQueue;
window.getCurrentQueueId = getCurrentQueueId;
window.setCurrentQueueId = setCurrentQueueId;
window.initializeQueueLinkCopyButtons = initializeQueueLinkCopyButtons;

console.log('✅ Queue Sharing component loaded');
