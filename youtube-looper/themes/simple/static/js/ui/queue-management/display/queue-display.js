// ============================================================================
// QUEUE DISPLAY COMPONENT
// ============================================================================

/**
 * Queue Display Component
 * Handles queue visualization, rendering, and UI updates
 * Responsible for displaying the current queue state and individual queue items
 */

/**
 * Update queue display
 * Main function to render the current queue state
 */
function updateQueueDisplay() {
  const queueContainer = document.getElementById('queue-container');
  const queueCount = document.getElementById('queue-count');
  const videoQueue = getVideoQueue();
  const currentVideoIndex = getCurrentVideoIndex();
  const isPlaying = getIsPlaying();

  queueCount.textContent = videoQueue.length;

  // Update queue action buttons visibility and state
  updateQueueActionButtons();

  // Update queue link display
  updateQueueLinkDisplay();

  if (videoQueue.length === 0) {
    renderEmptyQueue(queueContainer);
    return;
  }

  renderQueueItems(queueContainer, videoQueue, currentVideoIndex, isPlaying);

  // Add event listeners to queue item buttons
  addQueueItemEventListeners();

  // Add scroll prevention to queue items
  addQueueItemScrollPrevention();

  // Update scroll indicator
  updateQueueScrollIndicator();
}

/**
 * Render empty queue state
 * @param {HTMLElement} container - Queue container element
 */
function renderEmptyQueue(container) {
  container.innerHTML = `
    <div class="queue-empty">
      <div class="queue-empty-icon">♪</div>
      <p>Your queue is empty</p>
      <p>Add videos to start playing!</p>
    </div>
  `;
}

/**
 * Render queue items
 * @param {HTMLElement} container - Queue container element
 * @param {Array} videoQueue - Array of video objects
 * @param {number} currentVideoIndex - Index of currently playing video
 * @param {boolean} isPlaying - Whether queue is currently playing
 */
function renderQueueItems(container, videoQueue, currentVideoIndex, isPlaying) {
  let queueHTML = '';
  
  videoQueue.forEach((video, index) => {
    queueHTML += createQueueItemHTML(video, index, currentVideoIndex, isPlaying);
  });

  container.innerHTML = queueHTML;
}

/**
 * Create HTML for a single queue item
 * @param {Object} video - Video object
 * @param {number} index - Video index in queue
 * @param {number} currentVideoIndex - Index of currently playing video
 * @param {boolean} isPlaying - Whether queue is currently playing
 * @returns {string} HTML string for queue item
 */
function createQueueItemHTML(video, index, currentVideoIndex, isPlaying) {
  const isActive = index === currentVideoIndex && isPlaying;
  
  return `
    <div class="queue-item ${isActive ? 'active' : ''}">
      <div class="queue-item-thumbnail">
        <img src="${escapeHtml(video.thumbnail)}" alt="${escapeHtml(video.title)}" class="thumbnail-img" loading="lazy" />
        <div class="queue-item-number">${index + 1}</div>
        ${isActive ? '<div class="playing-indicator">▶</div>' : ''}
      </div>
      <div class="queue-item-info">
        <div class="queue-item-title" title="${escapeHtml(video.title)}">${escapeHtml(video.title)}</div>
        <div class="queue-item-meta">
          ${formatDuration(video.duration)} • ${escapeHtml(video.channel || 'YouTube')}
        </div>
      </div>
      <div class="queue-item-actions">
        <button class="queue-btn play-btn" data-index="${index}" title="${isActive ? 'Pause' : 'Play'} this video">
          ${isActive ? '⏸' : '▶'}
        </button>
        <button class="queue-btn copy-url-btn" data-index="${index}" title="Copy video URL">🔗</button>
      </div>
    </div>
  `;
}

/**
 * Update queue action buttons visibility and state
 */
function updateQueueActionButtons() {
  const videoQueue = getVideoQueue();
  const clearBtn = document.getElementById('clear-queue-btn');
  const playBtn = document.getElementById('play-queue-btn');
  const shareBtn = document.getElementById('share-queue-btn');

  // Show/hide buttons based on queue state
  if (clearBtn) {
    clearBtn.style.display = videoQueue.length > 0 ? 'block' : 'none';
  }

  if (playBtn) {
    playBtn.style.display = videoQueue.length > 0 ? 'block' : 'none';
    
    // Update play button text/icon
    const isPlaying = getIsPlaying();
    const playBtnText = playBtn.querySelector('.btn-text');
    const playBtnIcon = playBtn.querySelector('.btn-icon');
    
    if (playBtnText) {
      playBtnText.textContent = isPlaying ? 'Pause' : 'Play';
    }
    
    if (playBtnIcon) {
      playBtnIcon.textContent = isPlaying ? '⏸' : '▶';
    }
  }

  if (shareBtn) {
    shareBtn.style.display = videoQueue.length > 0 ? 'block' : 'none';
  }
}

/**
 * Update queue link display
 */
function updateQueueLinkDisplay() {
  const queueLinkSection = document.getElementById('queue-link-section');
  const queueLinkText = document.getElementById('queue-link-text');
  const videoQueue = getVideoQueue();
  const queueId = getCurrentQueueId();

  if (!queueLinkSection) return;

  if (videoQueue.length > 0 && queueId) {
    const queueUrl = `${window.location.origin}${window.location.pathname}?q=${queueId}`;
    
    if (queueLinkText) {
      queueLinkText.textContent = queueUrl;
    }
    
    queueLinkSection.style.display = 'block';
  } else {
    queueLinkSection.style.display = 'none';
  }
}

/**
 * Get queue statistics for display
 * @returns {Object} Queue statistics
 */
function getQueueStats() {
  const videoQueue = getVideoQueue();
  
  if (videoQueue.length === 0) {
    return {
      count: 0,
      totalDuration: 0,
      formattedDuration: '0:00',
      averageDuration: 0
    };
  }

  const totalDuration = videoQueue.reduce((total, video) => total + (video.duration || 0), 0);
  const averageDuration = Math.round(totalDuration / videoQueue.length);

  return {
    count: videoQueue.length,
    totalDuration,
    formattedDuration: formatDuration(totalDuration),
    averageDuration,
    formattedAverageDuration: formatDuration(averageDuration)
  };
}

/**
 * Update queue statistics display
 */
function updateQueueStatsDisplay() {
  const statsContainer = document.getElementById('queue-stats');
  
  if (!statsContainer) return;

  const stats = getQueueStats();
  
  if (stats.count === 0) {
    statsContainer.style.display = 'none';
    return;
  }

  statsContainer.innerHTML = `
    <div class="queue-stat">
      <span class="stat-label">Videos:</span>
      <span class="stat-value">${stats.count}</span>
    </div>
    <div class="queue-stat">
      <span class="stat-label">Total:</span>
      <span class="stat-value">${stats.formattedDuration}</span>
    </div>
    <div class="queue-stat">
      <span class="stat-label">Average:</span>
      <span class="stat-value">${stats.formattedAverageDuration}</span>
    </div>
  `;
  
  statsContainer.style.display = 'flex';
}

/**
 * Escape HTML to prevent XSS
 * @param {string} text - Text to escape
 * @returns {string} Escaped text
 */
function escapeHtml(text) {
  if (!text) return '';
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

/**
 * Refresh queue display
 * Force a complete re-render of the queue
 */
function refreshQueueDisplay() {
  console.log('🔄 Refreshing queue display...');
  updateQueueDisplay();
  updateQueueStatsDisplay();
}

// Export functions for global access
window.updateQueueDisplay = updateQueueDisplay;
window.refreshQueueDisplay = refreshQueueDisplay;
window.updateQueueActionButtons = updateQueueActionButtons;
window.updateQueueLinkDisplay = updateQueueLinkDisplay;
window.getQueueStats = getQueueStats;

console.log('✅ Queue Display component loaded');
