// ============================================================================
// QUEUE ITEMS COMPONENT
// ============================================================================

/**
 * Queue Items Component
 * Handles individual queue item interactions, event listeners, and item-specific functionality
 * Manages click events, copy functionality, and item state management
 */

/**
 * Add event listeners to queue item buttons
 */
function addQueueItemEventListeners() {
  // Play button listeners
  document.querySelectorAll('.queue-btn.play-btn').forEach(btn => {
    btn.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      
      const index = parseInt(this.getAttribute('data-index'));
      handleQueueItemPlay(index);
    });
  });

  // Copy URL button listeners
  document.querySelectorAll('.queue-btn.copy-url-btn').forEach(btn => {
    btn.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      
      const index = parseInt(this.getAttribute('data-index'));
      handleQueueItemCopyUrl(index);
    });
  });

  // Queue item click listeners (for mobile/touch)
  document.querySelectorAll('.queue-item').forEach(item => {
    item.addEventListener('click', function(e) {
      // Only handle clicks on the item itself, not buttons
      if (e.target.closest('.queue-item-actions')) {
        return;
      }
      
      const playBtn = this.querySelector('.queue-btn.play-btn');
      if (playBtn) {
        const index = parseInt(playBtn.getAttribute('data-index'));
        handleQueueItemPlay(index);
      }
    });
  });
}

/**
 * Handle play button click for queue item
 * @param {number} index - Index of the queue item
 */
function handleQueueItemPlay(index) {
  const videoQueue = getVideoQueue();
  const currentVideoIndex = getCurrentVideoIndex();
  const isPlaying = getIsPlaying();
  const player = getPlayer();

  if (index < 0 || index >= videoQueue.length) {
    console.log('Invalid queue item index:', index);
    return;
  }

  if (index === currentVideoIndex && isPlaying) {
    // Clicking on currently playing video - pause it
    if (player) {
      player.pauseVideo();
      setIsPlaying(false);
    }
  } else {
    // Clicking on different video or resuming - play it
    setCurrentVideoIndex(index);
    
    if (player) {
      player.loadVideoById(videoQueue[index].id);
      setIsPlaying(true);
    } else {
      createPlayer(videoQueue[index].id);
      setIsPlaying(true);
    }
  }

  updateQueueDisplay();
  updatePlayerControls();
  saveQueueToStorage();

  // Also update the shared queue if it exists
  autoUpdateSharedQueue();

  console.log(`🎵 Queue item ${index + 1}: ${videoQueue[index].title}`);
}

/**
 * Handle copy URL button click for queue item
 * @param {number} index - Index of the queue item
 */
async function handleQueueItemCopyUrl(index) {
  const videoQueue = getVideoQueue();

  if (index < 0 || index >= videoQueue.length) {
    console.log('Invalid queue item index:', index);
    return;
  }

  const video = videoQueue[index];
  const videoUrl = `https://www.youtube.com/watch?v=${video.id}`;

  try {
    await navigator.clipboard.writeText(videoUrl);
    
    // Show visual feedback
    showCopyFeedback(index);
    
    console.log(`📋 Copied URL for: ${video.title}`);
    
    // Show notification if available
    if (typeof showNotification === 'function') {
      showNotification(`Copied URL for "${video.title}"`, 'success');
    }
  } catch (error) {
    console.error('Failed to copy URL:', error);
    
    // Fallback: show URL in prompt
    prompt('Copy this URL:', videoUrl);
    
    if (typeof showNotification === 'function') {
      showNotification('URL copied to clipboard', 'info');
    }
  }
}

/**
 * Show visual feedback for copy action
 * @param {number} index - Index of the queue item
 */
function showCopyFeedback(index) {
  const copyBtn = document.querySelector(`.queue-btn.copy-url-btn[data-index="${index}"]`);
  
  if (copyBtn) {
    const originalText = copyBtn.innerHTML;
    copyBtn.innerHTML = '✓';
    copyBtn.classList.add('copied');
    
    setTimeout(() => {
      copyBtn.innerHTML = originalText;
      copyBtn.classList.remove('copied');
    }, 1000);
  }
}

/**
 * Add scroll prevention to queue items
 * Prevents wheel events from interfering with queue scrolling
 */
function addQueueItemScrollPrevention() {
  document.querySelectorAll('.header-current-queue .queue-item').forEach(item => {
    // Prevent wheel events from bubbling
    item.addEventListener('wheel', function(e) {
      e.preventDefault();
      e.stopPropagation();

      // Forward to queue container scroll
      const queueContainer = document.getElementById('queue-container');
      if (queueContainer) {
        if (e.deltaY > 0) {
          scrollQueueDown();
        } else if (e.deltaY < 0) {
          scrollQueueUp();
        }
      }
    }, { passive: false });

    // Prevent touch scroll
    item.addEventListener('touchmove', function(e) {
      e.preventDefault();
      e.stopPropagation();
    }, { passive: false });
  });
}

/**
 * Highlight queue item
 * @param {number} index - Index of item to highlight
 * @param {string} type - Type of highlight ('playing', 'selected', 'error')
 */
function highlightQueueItem(index, type = 'selected') {
  // Remove existing highlights
  document.querySelectorAll('.queue-item').forEach(item => {
    item.classList.remove('highlighted', 'highlighted-playing', 'highlighted-selected', 'highlighted-error');
  });

  // Add new highlight
  const item = document.querySelector(`.queue-item:nth-child(${index + 1})`);
  if (item) {
    item.classList.add('highlighted', `highlighted-${type}`);
    
    // Auto-remove highlight after delay (except for playing)
    if (type !== 'playing') {
      setTimeout(() => {
        item.classList.remove('highlighted', `highlighted-${type}`);
      }, 2000);
    }
  }
}

/**
 * Scroll to queue item
 * @param {number} index - Index of item to scroll to
 */
function scrollToQueueItem(index) {
  const queueContainer = document.getElementById('queue-container');
  const item = document.querySelector(`.queue-item:nth-child(${index + 1})`);
  
  if (queueContainer && item) {
    const containerRect = queueContainer.getBoundingClientRect();
    const itemRect = item.getBoundingClientRect();
    
    // Check if item is visible
    const isVisible = (
      itemRect.top >= containerRect.top &&
      itemRect.bottom <= containerRect.bottom
    );
    
    if (!isVisible) {
      // Scroll to make item visible
      const scrollTop = item.offsetTop - queueContainer.offsetTop - (containerRect.height / 2) + (itemRect.height / 2);
      
      queueContainer.scrollTo({
        top: scrollTop,
        behavior: 'smooth'
      });
    }
  }
}

/**
 * Get queue item element by index
 * @param {number} index - Index of the queue item
 * @returns {HTMLElement|null} Queue item element
 */
function getQueueItemElement(index) {
  return document.querySelector(`.queue-item:nth-child(${index + 1})`);
}

/**
 * Update queue item state
 * @param {number} index - Index of the queue item
 * @param {Object} state - State object with properties to update
 */
function updateQueueItemState(index, state) {
  const item = getQueueItemElement(index);
  
  if (!item) return;

  // Update active state
  if (state.hasOwnProperty('active')) {
    if (state.active) {
      item.classList.add('active');
    } else {
      item.classList.remove('active');
    }
  }

  // Update play button
  if (state.hasOwnProperty('playing')) {
    const playBtn = item.querySelector('.queue-btn.play-btn');
    const playingIndicator = item.querySelector('.playing-indicator');
    
    if (playBtn) {
      playBtn.innerHTML = state.playing ? '⏸' : '▶';
      playBtn.title = state.playing ? 'Pause this video' : 'Play this video';
    }
    
    if (state.playing && !playingIndicator) {
      const thumbnail = item.querySelector('.queue-item-thumbnail');
      if (thumbnail) {
        thumbnail.insertAdjacentHTML('beforeend', '<div class="playing-indicator">▶</div>');
      }
    } else if (!state.playing && playingIndicator) {
      playingIndicator.remove();
    }
  }

  // Update loading state
  if (state.hasOwnProperty('loading')) {
    if (state.loading) {
      item.classList.add('loading');
    } else {
      item.classList.remove('loading');
    }
  }

  // Update error state
  if (state.hasOwnProperty('error')) {
    if (state.error) {
      item.classList.add('error');
    } else {
      item.classList.remove('error');
    }
  }
}

/**
 * Initialize queue item interactions
 */
function initializeQueueItems() {
  console.log('🎵 Queue items initialized');
}

// Export functions for global access
window.addQueueItemEventListeners = addQueueItemEventListeners;
window.addQueueItemScrollPrevention = addQueueItemScrollPrevention;
window.highlightQueueItem = highlightQueueItem;
window.scrollToQueueItem = scrollToQueueItem;
window.updateQueueItemState = updateQueueItemState;

console.log('✅ Queue Items component loaded');
