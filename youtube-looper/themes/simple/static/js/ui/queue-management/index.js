// ============================================================================
// QUEUE MANAGEMENT CONTROLLER - MAIN INDEX
// ============================================================================

/**
 * Queue Management Controller Index
 * Coordinates all queue management components and provides unified API
 * This file serves as the main entry point for queue management functionality
 */

/**
 * Initialize all queue management components
 */
function initializeQueueManagement() {
  console.log('🎵 Initializing Queue Management Controller...');

  // Initialize all components
  initializeQueueDisplay();
  initializeQueueControls();
  initializeQueueItems();
  initializeQueueActions();
  initializeQueueScrolling();
  initializeQueueDraft();
  initializeQueueSharing();

  // Set up global event listeners
  setupGlobalQueueEvents();

  // Initial state update
  updateAllQueueComponents();

  console.log('✅ Queue Management Controller initialized');
}

/**
 * Initialize queue display component
 */
function initializeQueueDisplay() {
  // Queue display is initialized when the component loads
  console.log('📺 Queue display component ready');
}

/**
 * Initialize queue controls component
 */
function initializeQueueControls() {
  // Queue controls are initialized when the component loads
  console.log('🎮 Queue controls component ready');
}

/**
 * Initialize queue items component
 */
function initializeQueueItems() {
  // Queue items are initialized when the component loads
  console.log('🎵 Queue items component ready');
}

/**
 * Initialize queue actions component
 */
function initializeQueueActions() {
  // Queue actions are initialized when the component loads
  console.log('⚡ Queue actions component ready');
}

/**
 * Initialize queue scrolling component
 */
function initializeQueueScrolling() {
  // Queue scrolling is initialized when the component loads
  console.log('📜 Queue scrolling component ready');
}

/**
 * Initialize queue draft component
 */
function initializeQueueDraft() {
  // Queue draft is initialized when the component loads
  console.log('📝 Queue draft component ready');
}

/**
 * Initialize queue sharing component
 */
function initializeQueueSharing() {
  // Queue sharing is initialized when the component loads
  console.log('🔗 Queue sharing component ready');
}

/**
 * Set up global event listeners for queue management
 */
function setupGlobalQueueEvents() {
  // Listen for queue state changes
  document.addEventListener('queueStateChanged', handleQueueStateChange);
  
  // Listen for player state changes
  document.addEventListener('playerStateChanged', handlePlayerStateChange);
  
  // Listen for window resize to update scroll indicators
  window.addEventListener('resize', debounce(updateQueueScrollIndicator, 250));
  
  console.log('🔗 Global queue events set up');
}

/**
 * Handle queue state changes
 * @param {CustomEvent} event - Queue state change event
 */
function handleQueueStateChange(event) {
  const { type, data } = event.detail || {};
  
  console.log('🔄 Queue state changed:', type, data);
  
  // Update all components when queue state changes
  updateAllQueueComponents();
  
  // Emit analytics event if available
  if (typeof trackEvent === 'function') {
    trackEvent('queue_state_change', { type, data });
  }
}

/**
 * Handle player state changes
 * @param {CustomEvent} event - Player state change event
 */
function handlePlayerStateChange(event) {
  const { state, data } = event.detail || {};
  
  console.log('🎵 Player state changed:', state, data);
  
  // Update display to reflect player state
  updateQueueDisplay();
  updateQueueActionButtons();
  
  // Handle video end
  if (state === 'ended') {
    handleVideoEnd();
  }
}

/**
 * Update all queue components
 */
function updateAllQueueComponents() {
  // Update display
  if (typeof updateQueueDisplay === 'function') {
    updateQueueDisplay();
  }
  
  // Update scroll indicators
  if (typeof updateQueueScrollIndicator === 'function') {
    updateQueueScrollIndicator();
  }
  
  // Update action buttons
  if (typeof updateQueueActionButtons === 'function') {
    updateQueueActionButtons();
  }
  
  // Update statistics
  if (typeof updateQueueStatsDisplay === 'function') {
    updateQueueStatsDisplay();
  }
}

/**
 * Refresh entire queue management interface
 */
function refreshQueueManagement() {
  console.log('🔄 Refreshing queue management interface...');
  
  updateAllQueueComponents();
  
  // Re-initialize event listeners for dynamic content
  if (typeof addQueueItemEventListeners === 'function') {
    addQueueItemEventListeners();
  }
  
  if (typeof addQueueItemScrollPrevention === 'function') {
    addQueueItemScrollPrevention();
  }
  
  console.log('✅ Queue management interface refreshed');
}

/**
 * Get queue management state
 * @returns {Object} Current queue management state
 */
function getQueueManagementState() {
  const videoQueue = getVideoQueue();
  const currentVideoIndex = getCurrentVideoIndex();
  const isPlaying = getIsPlaying();
  
  return {
    queue: videoQueue,
    currentIndex: currentVideoIndex,
    isPlaying: isPlaying,
    queueLength: videoQueue.length,
    hasQueue: videoQueue.length > 0,
    canGoNext: currentVideoIndex < videoQueue.length - 1,
    canGoPrevious: currentVideoIndex > 0,
    currentVideo: videoQueue[currentVideoIndex] || null,
    stats: typeof getQueueStats === 'function' ? getQueueStats() : null
  };
}

/**
 * Emit queue state change event
 * @param {string} type - Type of change
 * @param {*} data - Additional data
 */
function emitQueueStateChange(type, data = null) {
  const event = new CustomEvent('queueStateChanged', {
    detail: { type, data, state: getQueueManagementState() }
  });
  
  document.dispatchEvent(event);
}

/**
 * Debounce function to limit event frequency
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 */
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * Queue Management API
 * Provides a unified interface for all queue operations
 */
const QueueManagementAPI = {
  // State
  getState: getQueueManagementState,
  refresh: refreshQueueManagement,
  
  // Display
  updateDisplay: () => typeof updateQueueDisplay === 'function' && updateQueueDisplay(),
  refreshDisplay: () => typeof refreshQueueDisplay === 'function' && refreshQueueDisplay(),
  
  // Controls
  play: () => typeof playQueue === 'function' && playQueue(),
  previous: () => typeof previousVideo === 'function' && previousVideo(),
  next: () => typeof nextVideo === 'function' && nextVideo(),
  jumpTo: (index) => typeof jumpToVideo === 'function' && jumpToVideo(index),
  
  // Actions
  addVideo: (video, playImmediately) => typeof addVideoToQueue === 'function' && addVideoToQueue(video, playImmediately),
  removeVideo: (index) => typeof removeVideoFromQueue === 'function' && removeVideoFromQueue(index),
  clear: () => typeof clearQueue === 'function' && clearQueue(),
  shuffle: () => typeof shuffleQueue === 'function' && shuffleQueue(),
  
  // Scrolling
  scrollUp: () => typeof scrollQueueUp === 'function' && scrollQueueUp(),
  scrollDown: () => typeof scrollQueueDown === 'function' && scrollQueueDown(),
  scrollToItem: (index) => typeof scrollToQueueItem === 'function' && scrollToQueueItem(index),
  scrollToCurrent: () => typeof autoScrollToCurrentItem === 'function' && autoScrollToCurrentItem(),

  // Draft Queue
  getDraft: () => typeof getDraftQueue === 'function' && getDraftQueue(),
  addToDraft: (video) => typeof addVideoToDraftQueue === 'function' && addVideoToDraftQueue(video),
  removeFromDraft: (index) => typeof removeVideoFromDraftQueue === 'function' && removeVideoFromDraftQueue(index),
  clearDraft: () => typeof clearDraftQueue === 'function' && clearDraftQueue(),
  enterCreationMode: () => typeof enterQueueCreationMode === 'function' && enterQueueCreationMode(),
  exitCreationMode: () => typeof exitQueueCreationMode === 'function' && exitQueueCreationMode(),
  isInCreationMode: () => typeof isInQueueCreationMode === 'function' && isInQueueCreationMode(),

  // Sharing
  share: (queueId) => typeof shareCurrentQueue === 'function' && shareCurrentQueue(queueId),
  copyLink: (queueId) => typeof copyQueueLink === 'function' && copyQueueLink(queueId),
  getSharingStatus: () => typeof getQueueSharingStatus === 'function' && getQueueSharingStatus(),
  updateSharingUI: () => typeof updateSharingUI === 'function' && updateSharingUI(),

  // Events
  emit: emitQueueStateChange
};

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeQueueManagement);
} else {
  // Small delay to ensure all components are loaded
  setTimeout(initializeQueueManagement, 50);
}

// Export API for global access
window.QueueManagement = QueueManagementAPI;
window.initializeQueueManagement = initializeQueueManagement;
window.refreshQueueManagement = refreshQueueManagement;
window.getQueueManagementState = getQueueManagementState;
window.emitQueueStateChange = emitQueueStateChange;

console.log('✅ Queue Management Controller Index loaded');
