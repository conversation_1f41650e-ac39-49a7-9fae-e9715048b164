// ============================================================================
// QUEUE ACTIONS COMPONENT
// ============================================================================

/**
 * Queue Actions Component
 * Handles queue manipulation actions like add, remove, clear, and reorder
 * Manages queue state changes and persistence
 */

/**
 * Add video to queue
 * @param {Object} video - Video object to add
 * @param {boolean} playImmediately - Whether to play the video immediately
 * @returns {boolean} True if video was added successfully
 */
function addVideoToQueue(video, playImmediately = false) {
  if (!video || !video.id) {
    console.log('Invalid video object');
    return false;
  }

  const videoQueue = getVideoQueue();
  
  // Check if video already exists in queue
  const existingIndex = videoQueue.findIndex(v => v.id === video.id);
  if (existingIndex !== -1) {
    console.log('Video already in queue');
    
    // If requested to play immediately, jump to existing video
    if (playImmediately) {
      jumpToVideo(existingIndex);
    }
    
    return false;
  }

  // Add video to queue
  videoQueue.push(video);
  setVideoQueue(videoQueue);

  // If this is the first video or play immediately requested
  if (videoQueue.length === 1 || playImmediately) {
    const targetIndex = playImmediately ? videoQueue.length - 1 : 0;
    setCurrentVideoIndex(targetIndex);
    
    const player = getPlayer();
    if (player) {
      player.loadVideoById(video.id);
      setIsPlaying(true);
    } else {
      createPlayer(video.id);
      setIsPlaying(true);
    }
  }

  updateQueueDisplay();
  updatePlayerControls();
  saveQueueToStorage();

  // Also update the shared queue if it exists
  autoUpdateSharedQueue();

  console.log(`➕ Added to queue: ${video.title}`);
  
  // Show notification if available
  if (typeof showNotification === 'function') {
    showNotification(`Added "${video.title}" to queue`, 'success');
  }

  return true;
}

/**
 * Remove video from queue
 * @param {number} index - Index of video to remove
 * @returns {boolean} True if video was removed successfully
 */
function removeVideoFromQueue(index) {
  const videoQueue = getVideoQueue();
  const currentVideoIndex = getCurrentVideoIndex();
  const isPlaying = getIsPlaying();
  const player = getPlayer();

  if (index < 0 || index >= videoQueue.length) {
    console.log('Invalid queue index:', index);
    return false;
  }

  const removedVideo = videoQueue[index];

  // If removing currently playing video
  if (index === currentVideoIndex && isPlaying) {
    if (videoQueue.length > 1) {
      // If there are other videos, play the next one
      const nextIndex = index === videoQueue.length - 1 ? 0 : index;
      videoQueue.splice(index, 1);

      // Adjust currentVideoIndex if needed
      if (index === currentVideoIndex) {
        setCurrentVideoIndex(nextIndex >= videoQueue.length ? 0 : nextIndex);
      } else if (index < currentVideoIndex) {
        setCurrentVideoIndex(currentVideoIndex - 1);
      }

      if (player) {
        player.loadVideoById(videoQueue[getCurrentVideoIndex()].id);
      }
    } else {
      // If it's the only video
      setVideoQueue([]);
      setCurrentVideoIndex(0);
      if (player) {
        player.stopVideo();
        setIsPlaying(false);
      }
    }
  } else {
    // If removing a video that's not playing
    videoQueue.splice(index, 1);

    // Adjust currentVideoIndex if needed
    if (index < currentVideoIndex) {
      setCurrentVideoIndex(currentVideoIndex - 1);
    }
  }

  updateQueueDisplay();
  updatePlayerControls();
  saveQueueToStorage();

  // Update queue controls visibility
  if (typeof updateQueueControls === 'function') {
    updateQueueControls();
  }

  // Also update the shared queue if it exists
  autoUpdateSharedQueue();

  console.log(`➖ Removed from queue: ${removedVideo.title}`);
  
  // Show notification if available
  if (typeof showNotification === 'function') {
    showNotification(`Removed "${removedVideo.title}" from queue`, 'info');
  }

  return true;
}

/**
 * Clear the entire queue
 */
function clearQueue() {
  const videoQueue = getVideoQueue();
  
  if (videoQueue.length === 0) {
    console.log('Queue is already empty');
    return;
  }

  // Confirm action for non-empty queues
  const confirmMessage = `Are you sure you want to clear the entire queue?\n\nThis will remove all ${videoQueue.length} video(s) and cannot be undone.`;
  if (!confirm(confirmMessage)) {
    return;
  }

  const player = getPlayer();

  setVideoQueue([]);
  setCurrentVideoIndex(0);

  // Switch back to personal mode when clearing queue
  if (typeof switchToPersonalMode === 'function') {
    switchToPersonalMode();
  } else {
    // Fallback: Clear the queue ID since queue is empty
    try {
      sessionStorage.removeItem('currentQueueId');
    } catch (error) {
      console.warn('Could not clear queue ID from session storage:', error);
    }
  }

  if (player) {
    player.stopVideo();
    setIsPlaying(false);
  }

  updateQueueDisplay();
  updatePlayerControls();
  clearQueueFromStorage();

  // Update queue controls visibility
  if (typeof updateQueueControls === 'function') {
    updateQueueControls();
  }

  console.log('🗑️ Queue cleared');
  
  // Show notification if available
  if (typeof showNotification === 'function') {
    showNotification('Queue cleared', 'info');
  }
}

/**
 * Move video to new position in queue
 * @param {number} fromIndex - Current index of video
 * @param {number} toIndex - New index for video
 * @returns {boolean} True if video was moved successfully
 */
function moveVideoInQueue(fromIndex, toIndex) {
  const videoQueue = getVideoQueue();
  const currentVideoIndex = getCurrentVideoIndex();

  if (fromIndex < 0 || fromIndex >= videoQueue.length || 
      toIndex < 0 || toIndex >= videoQueue.length ||
      fromIndex === toIndex) {
    console.log('Invalid move operation');
    return false;
  }

  // Remove video from old position
  const video = videoQueue.splice(fromIndex, 1)[0];
  
  // Insert video at new position
  videoQueue.splice(toIndex, 0, video);

  // Update current video index if necessary
  let newCurrentIndex = currentVideoIndex;
  
  if (fromIndex === currentVideoIndex) {
    // Moving the currently playing video
    newCurrentIndex = toIndex;
  } else if (fromIndex < currentVideoIndex && toIndex >= currentVideoIndex) {
    // Moving a video from before current to after current
    newCurrentIndex = currentVideoIndex - 1;
  } else if (fromIndex > currentVideoIndex && toIndex <= currentVideoIndex) {
    // Moving a video from after current to before current
    newCurrentIndex = currentVideoIndex + 1;
  }

  setCurrentVideoIndex(newCurrentIndex);
  setVideoQueue(videoQueue);

  updateQueueDisplay();
  updatePlayerControls();
  saveQueueToStorage();

  // Also update the shared queue if it exists
  autoUpdateSharedQueue();

  console.log(`🔄 Moved "${video.title}" from position ${fromIndex + 1} to ${toIndex + 1}`);
  
  return true;
}

/**
 * Duplicate video in queue
 * @param {number} index - Index of video to duplicate
 * @returns {boolean} True if video was duplicated successfully
 */
function duplicateVideoInQueue(index) {
  const videoQueue = getVideoQueue();

  if (index < 0 || index >= videoQueue.length) {
    console.log('Invalid queue index:', index);
    return false;
  }

  const video = { ...videoQueue[index] };
  
  // Insert duplicate right after the original
  videoQueue.splice(index + 1, 0, video);
  setVideoQueue(videoQueue);

  // Adjust current video index if necessary
  const currentVideoIndex = getCurrentVideoIndex();
  if (index < currentVideoIndex) {
    setCurrentVideoIndex(currentVideoIndex + 1);
  }

  updateQueueDisplay();
  updatePlayerControls();
  saveQueueToStorage();

  // Also update the shared queue if it exists
  autoUpdateSharedQueue();

  console.log(`📋 Duplicated: ${video.title}`);
  
  // Show notification if available
  if (typeof showNotification === 'function') {
    showNotification(`Duplicated "${video.title}"`, 'success');
  }

  return true;
}

/**
 * Shuffle queue order
 */
function shuffleQueue() {
  const videoQueue = getVideoQueue();
  const currentVideoIndex = getCurrentVideoIndex();

  if (videoQueue.length <= 1) {
    console.log('Cannot shuffle queue with less than 2 videos');
    return;
  }

  // Remove currently playing video temporarily
  const currentVideo = videoQueue[currentVideoIndex];
  const remainingVideos = videoQueue.filter((_, index) => index !== currentVideoIndex);

  // Shuffle remaining videos
  for (let i = remainingVideos.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [remainingVideos[i], remainingVideos[j]] = [remainingVideos[j], remainingVideos[i]];
  }

  // Put current video back at the beginning
  const shuffledQueue = [currentVideo, ...remainingVideos];
  
  setVideoQueue(shuffledQueue);
  setCurrentVideoIndex(0);

  updateQueueDisplay();
  updatePlayerControls();
  saveQueueToStorage();

  // Also update the shared queue if it exists
  autoUpdateSharedQueue();

  console.log('🔀 Queue shuffled');
  
  // Show notification if available
  if (typeof showNotification === 'function') {
    showNotification('Queue shuffled', 'info');
  }
}

/**
 * Reverse queue order
 */
function reverseQueue() {
  const videoQueue = getVideoQueue();
  const currentVideoIndex = getCurrentVideoIndex();

  if (videoQueue.length <= 1) {
    console.log('Cannot reverse queue with less than 2 videos');
    return;
  }

  // Reverse the queue
  const reversedQueue = [...videoQueue].reverse();
  const newCurrentIndex = videoQueue.length - 1 - currentVideoIndex;

  setVideoQueue(reversedQueue);
  setCurrentVideoIndex(newCurrentIndex);

  updateQueueDisplay();
  updatePlayerControls();
  saveQueueToStorage();

  // Also update the shared queue if it exists
  autoUpdateSharedQueue();

  console.log('🔄 Queue reversed');
  
  // Show notification if available
  if (typeof showNotification === 'function') {
    showNotification('Queue reversed', 'info');
  }
}

// Export functions for global access
window.addVideoToQueue = addVideoToQueue;
window.removeVideoFromQueue = removeVideoFromQueue;
window.clearQueue = clearQueue;
window.moveVideoInQueue = moveVideoInQueue;
window.duplicateVideoInQueue = duplicateVideoInQueue;
window.shuffleQueue = shuffleQueue;
window.reverseQueue = reverseQueue;

console.log('✅ Queue Actions component loaded');
