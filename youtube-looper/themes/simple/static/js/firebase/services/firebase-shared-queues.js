// ============================================================================
// FIREBASE SHARED QUEUES FUNCTIONALITY
// ============================================================================

// Global variable to track if we're viewing a shared queue
let isViewingSharedQueue = false;
window.isViewingSharedQueue = false;

/**
 * Save queue to Firebase Firestore for public sharing
 * @param {string|null} queueId - Optional queue ID, generates new if null
 * @param {boolean} isAutoUpdate - True if called from auto-update (don't show error messages)
 * @returns {Promise<string|null>} - Queue ID if successful, null if failed
 */
async function saveQueueToFirebase(queueId = null, isAutoUpdate = false) {
  if (!isFirebaseInitialized()) {
    console.log('Firebase not initialized');
    return null;
  }

  // Use the new service layer
  return await window.publicQueueService.saveCurrentQueue(queueId, isAutoUpdate);
}

/**
 * Load queue from Firebase Firestore
 * @param {string} queueId - Queue ID to load
 * @returns {Promise<boolean>} - True if successful
 */
async function loadQueueFromFirebase(queueId) {
  if (!isFirebaseInitialized()) {
    console.log('Firebase not initialized');
    return false;
  }

  // Use the new service layer
  return await window.publicQueueService.loadQueue(queueId);
}

/**
 * Check if currently viewing a shared queue
 * @returns {boolean} - True if viewing shared queue
 */
function isInSharedMode() {
  return isViewingSharedQueue;
}

/**
 * Switch to personal queue mode (stop viewing shared queue)
 */
async function switchToPersonalMode() {
  isViewingSharedQueue = false;
  window.isViewingSharedQueue = false;

  // Clear the shared queue ID
  try {
    sessionStorage.removeItem('currentQueueId');
  } catch (error) {
    console.warn('Could not clear queue ID from session storage:', error);
  }

  console.log('👤 Switched to personal mode - personal queue auto-save enabled');

  // Load personal queue from Firebase
  try {
    await loadQueueFromStorage();
    console.log('✅ Personal queue loaded');
  } catch (error) {
    console.warn('Could not load personal queue:', error);
    // Clear the queue if we can't load personal queue
    if (typeof setVideoQueue === 'function') {
      setVideoQueue([]);
      setCurrentVideoIndex(0);
    }
  }

  // Update UI
  if (typeof updateQueueDisplay === 'function') {
    updateQueueDisplay();
  }
  if (typeof updatePlayerControls === 'function') {
    updatePlayerControls();
  }
  if (typeof updateQueueLinkDisplay === 'function') {
    updateQueueLinkDisplay();
  }
}

/**
 * List queues from Firebase Firestore
 * @param {string} sortBy - Sort criteria ('recent', 'popular', 'newest', 'longest')
 * @param {number} limit - Maximum number of queues to return
 * @returns {Promise<Object>} - Result object with success status and data
 */
async function listQueuesFromFirebase(sortBy = 'recent', limit = 50) {
  if (!isFirebaseInitialized()) {
    return {
      success: false,
      message: 'Firebase not initialized'
    };
  }

  // Use the new service layer
  return await window.publicQueueService.listQueues({ sortBy, limit });
}

console.log('✅ Firebase Shared Queues module loaded');
