// ============================================================================
// FIREBASE PERSONAL QUEUES FUNCTIONALITY
// ============================================================================

/**
 * Save draft queue as a personal queue
 * @param {string} title - Queue title
 * @param {Array} draftQueue - Draft queue videos
 * @returns {Promise<string|null>} - Queue ID if successful
 */
async function savePersonalQueueFromDraft(title, draftQueue) {
  console.log('💾 savePersonalQueueFromDraft called with:', { title, draftQueue });

  if (!draftQueue || draftQueue.length === 0) {
    console.log('❌ Cannot save empty draft queue');
    console.log('Cannot save empty draft queue');
    return null;
  }

  if (!isFirebaseInitialized()) {
    console.log('❌ Firebase not initialized');
    console.log('Firebase not initialized');
    return null;
  }

  // Get current user
  const auth = getFirebaseAuth();
  const user = auth.currentUser;

  if (!user) {
    console.log('❌ No authenticated user');
    console.log('User not authenticated');
    return null;
  }

  // Use the new service layer to save draft queue
  try {
    // Create queue data model from draft
    const queueData = new QueueDataModel({
      [QueueDataModel.FIELDS.QUEUE]: draftQueue,
      [QueueDataModel.FIELDS.CURRENT_INDEX]: 0,
      [QueueDataModel.FIELDS.IS_PLAYING]: false,
      [QueueDataModel.FIELDS.TIMESTAMP]: Date.now(),
      [QueueDataModel.FIELDS.TITLE]: title
    });

    // Generate unique queue ID
    const queueId = `personal_${user.uid}_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;

    // Create personal queue model
    const personalQueue = new PersonalQueueModel({
      [PersonalQueueModel.FIELDS.ID]: queueId,
      [PersonalQueueModel.FIELDS.USER_ID]: user.uid,
      [PersonalQueueModel.FIELDS.QUEUE_DATA]: queueData.toObject(),
      [PersonalQueueModel.FIELDS.IS_PERSONAL]: true,
      [PersonalQueueModel.FIELDS.IS_PUBLIC]: false
    });

    // Save using repository
    const savedQueueId = await window.personalQueueRepo.save(personalQueue);

    // Invalidate personal queues cache since new queue was added
    if (typeof invalidatePersonalQueuesCache === 'function') {
      invalidatePersonalQueuesCache();
    }

    console.log(`Queue "${title}" saved successfully`);
    return savedQueueId;

  } catch (error) {
    console.error('❌ Error saving personal queue to Firebase:', error);
    console.log('Failed to save queue:', error.message);
    return null;
  }
}

/**
 * Load and play a personal queue
 * @param {string} queueId - Queue ID to load and play
 */
async function loadPersonalQueueAndPlay(queueId) {
  console.log('🎵 loadPersonalQueueAndPlay called with:', queueId);

  if (!isFirebaseInitialized()) {
    console.log('❌ Firebase not initialized');
    console.log('Firebase not initialized');
    return false;
  }

  // Get current user
  const auth = getFirebaseAuth();
  const user = auth.currentUser;

  if (!user) {
    console.log('❌ No authenticated user');
    console.log('User not authenticated');
    return false;
  }

  try {
    // Use the new service layer
    const success = await window.personalQueueService.loadAndPlayQueue(queueId);

    if (success) {
      // Auto-play the first video
      setTimeout(() => {
        playQueue();
      }, 500);

      console.log('✅ Personal queue loaded and playing');
      console.log('Queue loaded and playing');
    }

    return success;

  } catch (error) {
    console.error('❌ Error loading personal queue:', error);
    console.log('Failed to load queue:', error.message);
    return false;
  }
}

/**
 * Save a named personal queue to Firebase (legacy function - not actively used)
 * @param {string} userId - User ID
 * @param {string} queueTitle - Title for the queue
 * @returns {Promise<string|null>} - Queue ID if successful, null if failed
 */
async function savePersonalQueueToFirebase(userId, queueTitle = null) {
  console.log('💾 savePersonalQueueToFirebase called with:', { userId, queueTitle });

  const videoQueue = getVideoQueue();
  console.log('📋 Current video queue:', videoQueue);

  if (videoQueue.length === 0) {
    console.log('❌ Cannot save empty queue');
    console.log('Cannot save empty queue');
    return null;
  }

  if (!isFirebaseInitialized()) {
    console.log('❌ Firebase not initialized');
    console.log('Firebase not initialized');
    return null;
  }

  if (!userId) {
    console.log('❌ No user ID provided');
    console.log('User not signed in');
    return null;
  }

  // Generate a unique ID for this personal queue
  const queueId = `personal_${userId}_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  console.log('🆔 Generated queue ID:', queueId);

  // Use provided title or generate one
  const title = queueTitle || `Personal Queue - ${new Date().toLocaleDateString()}`;
  console.log('📝 Queue title:', title);

  const queueData = {
    queue: videoQueue,
    currentIndex: getCurrentVideoIndex(),
    isPlaying: getIsPlaying(),
    timestamp: Date.now(),
    title: title
  };

  try {
    const db = getFirebaseDb();
    const timestamp = firebase.firestore.FieldValue.serverTimestamp();

    // Prepare document data for personal queue
    const docData = {
      id: queueId,
      userId: userId,
      queueData: queueData,
      lastModified: timestamp,
      createdAt: timestamp,
      isPersonal: true
    };

    console.log('💾 Saving document data:', docData);

    // Save to personal_queues collection
    const docRef = db.collection('personal_queues').doc(queueId);
    await docRef.set(docData);

    console.log('✅ Personal queue saved to Firebase:', queueId);

    // Invalidate personal queues cache since new queue was added
    if (typeof invalidatePersonalQueuesCache === 'function') {
      invalidatePersonalQueuesCache();
    }

    return queueId;

  } catch (error) {
    console.error('❌ Error saving personal queue to Firebase:', error);
    console.log('Failed to save personal queue:', error.message);
    return null;
  }
}

console.log('✅ Firebase Personal Queues module loaded');
