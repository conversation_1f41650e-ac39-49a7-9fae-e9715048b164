// ============================================================================
// FIREBASE SERVICE LAYER
// ============================================================================

/**
 * Service for managing public shared queues
 */
class PublicQueueService {
  constructor() {
    this.repository = window.publicQueueRepo;
  }

  /**
   * Create and save a public queue from current queue
   * @param {string|null} queueId - Optional existing queue ID
   * @param {boolean} isAutoUpdate - Whether this is an auto-update
   * @returns {Promise<string|null>} Queue ID or null if failed
   */
  async saveCurrentQueue(queueId = null, isAutoUpdate = false) {
    try {
      const videoQueue = getVideoQueue();
      
      if (videoQueue.length === 0) {
        if (queueId && isAutoUpdate) {
          await this.repository.delete(queueId);
          console.log('🗑️ Deleted empty shared queue:', queueId);
          return queueId;
        } else {
          console.log('Cannot share empty queue');
          return null;
        }
      }

      // Create queue data model
      const queueData = new QueueDataModel({
        [QueueDataModel.FIELDS.QUEUE]: videoQueue,
        [QueueDataModel.FIELDS.CURRENT_INDEX]: getCurrentVideoIndex(),
        [QueueDataModel.FIELDS.IS_PLAYING]: getIsPlaying(),
        [QueueDataModel.FIELDS.TIMESTAMP]: Date.now(),
        [QueueDataModel.FIELDS.TITLE]: `Queue shared on ${new Date().toLocaleDateString()}`
      });

      // Create metadata from queue data
      const metadata = QueueMetadataModel.fromQueueData(queueData);
      
      // Create public queue model
      const publicQueue = new PublicQueueModel({
        [PublicQueueModel.FIELDS.ID]: queueId,
        [PublicQueueModel.FIELDS.QUEUE_DATA]: queueData.toObject(),
        [PublicQueueModel.FIELDS.METADATA]: metadata.toObject()
      });

      // If updating existing queue, preserve view count and creation date
      if (queueId) {
        const existingQueue = await this.repository.load(queueId);
        if (existingQueue) {
          metadata.viewCount = existingQueue.metadata.viewCount;
          metadata.createdAt = existingQueue.metadata.createdAt;
        }
      }

      const savedQueueId = await this.repository.save(publicQueue);

      if (!isAutoUpdate) {
        console.log('Queue saved successfully');
        
        // Copy to clipboard
        try {
          await navigator.clipboard.writeText(savedQueueId);
          console.log('Queue ID copied to clipboard');
        } catch (clipboardError) {
          console.warn('Could not copy to clipboard:', clipboardError);
        }
      }

      return savedQueueId;

    } catch (error) {
      console.error('Error saving queue to Firebase:', error);
      if (!isAutoUpdate) {
        console.log('Failed to save queue to cloud:', error.message);
      }
      return null;
    }
  }

  /**
   * Load a public queue and set as current
   * @param {string} queueId - Queue ID to load
   * @returns {Promise<boolean>} True if successful
   */
  async loadQueue(queueId) {
    try {
      if (!queueId || queueId.trim() === '') {
        console.log('Invalid Queue ID provided');
        return false;
      }

      console.log('Loading queue from cloud...');

      const publicQueue = await this.repository.load(queueId.trim());
      
      if (!publicQueue) {
        console.log('Queue not found:', queueId);
        return false;
      }

      const queueData = publicQueue.queueData;

      // Load the queue data
      setVideoQueue(queueData.queue.map(video => video.toObject()));
      setCurrentVideoIndex(queueData.currentIndex);
      setIsPlaying(false); // Don't auto-play loaded queues

      // Ensure currentVideoIndex is within bounds
      if (getCurrentVideoIndex() >= getVideoQueue().length) {
        setCurrentVideoIndex(0);
      }

      // Set the loaded queue ID as current queue ID
      try {
        sessionStorage.setItem('currentQueueId', queueId.trim());
        console.log('🔗 Set current queue ID to loaded queue:', queueId.trim());
      } catch (error) {
        console.warn('Could not save queue ID to session storage:', error);
      }

      // Mark that we're now viewing a shared queue
      isViewingSharedQueue = true;
      window.isViewingSharedQueue = true;
      console.log('🔗 Now viewing shared queue - personal queue auto-save disabled');

      // Increment view count
      await this.repository.incrementViewCount(queueId.trim());

      // Update UI
      updateQueueDisplay();
      updatePlayerControls();
      saveQueueToStorage(); // Save to local storage too

      // Update queue link display
      if (typeof updateQueueLinkDisplay === 'function') {
        updateQueueLinkDisplay();
      }

      console.log(`Loaded ${getVideoQueue().length} video(s) from shared queue`);
      return true;

    } catch (error) {
      console.error('Error loading queue from Firebase:', error);
      console.log('Failed to load queue from cloud:', error);
      return false;
    }
  }

  /**
   * List public queues with filtering and sorting
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Result with queues and metadata
   */
  async listQueues(options = {}) {
    try {
      const {
        sortBy = 'recent',
        limit = 50
      } = options;

      const queues = await this.repository.list({ sortBy, limit });

      // Transform to legacy format for compatibility
      const transformedQueues = queues.map(queue => {
        const queueData = queue.queueData;
        const metadata = queue.metadata;

        return {
          queueId: queue.id,
          lastModified: metadata.lastModified?.toDate?.() || new Date(),
          title: metadata.title || 'Untitled Queue',
          viewCount: metadata.viewCount || 0,
          createdDate: metadata.createdAt?.toDate?.() || new Date(),
          videoCount: metadata.videoCount || 0,
          totalDuration: metadata.totalDuration || 0,
          thumbnail: metadata.firstVideoThumbnail || '',
          preview: queueData.queue ? queueData.queue.slice(0, 3).map(v => ({
            title: v.title,
            thumbnail: v.thumbnail,
            duration: v.duration
          })) : []
        };
      });

      console.log('Queues listed from Firebase:', transformedQueues.length);
      return {
        success: true,
        message: 'Queues listed successfully',
        data: {
          queues: transformedQueues,
          total: transformedQueues.length,
          sortBy: sortBy,
          limit: limit
        }
      };

    } catch (error) {
      console.error('Error listing queues from Firebase:', error);
      return {
        success: false,
        message: 'Failed to list queues: ' + error.message
      };
    }
  }
}

/**
 * Service for managing personal queues
 */
class PersonalQueueService {
  constructor() {
    this.repository = window.personalQueueRepo;
  }

  /**
   * Save current queue as personal queue
   * @param {string} userId - User ID
   * @param {string} title - Queue title
   * @returns {Promise<string|null>} Queue ID or null if failed
   */
  async saveCurrentQueue(userId, title) {
    try {
      const videoQueue = getVideoQueue();
      
      if (videoQueue.length === 0) {
        console.log('Cannot save empty queue');
        return null;
      }

      // Generate unique queue ID
      const queueId = `personal_${userId}_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;

      // Create queue data model
      const queueData = new QueueDataModel({
        [QueueDataModel.FIELDS.QUEUE]: videoQueue,
        [QueueDataModel.FIELDS.CURRENT_INDEX]: getCurrentVideoIndex(),
        [QueueDataModel.FIELDS.IS_PLAYING]: getIsPlaying(),
        [QueueDataModel.FIELDS.TIMESTAMP]: Date.now(),
        [QueueDataModel.FIELDS.TITLE]: title
      });

      // Create personal queue model
      const personalQueue = new PersonalQueueModel({
        [PersonalQueueModel.FIELDS.ID]: queueId,
        [PersonalQueueModel.FIELDS.USER_ID]: userId,
        [PersonalQueueModel.FIELDS.QUEUE_DATA]: queueData.toObject(),
        [PersonalQueueModel.FIELDS.IS_PERSONAL]: true,
        [PersonalQueueModel.FIELDS.IS_PUBLIC]: false
      });

      const savedQueueId = await this.repository.save(personalQueue);

      // Invalidate cache
      if (typeof invalidatePersonalQueuesCache === 'function') {
        invalidatePersonalQueuesCache();
      }

      console.log(`Queue "${title}" saved successfully`);
      return savedQueueId;

    } catch (error) {
      console.error('Error saving personal queue:', error);
      console.log('Failed to save queue:', error.message);
      return null;
    }
  }

  /**
   * Load and play a personal queue
   * @param {string} queueId - Queue ID
   * @returns {Promise<boolean>} True if successful
   */
  async loadAndPlayQueue(queueId) {
    try {
      console.log('🎵 Loading personal queue:', queueId);

      const personalQueue = await this.repository.load(queueId);
      
      if (!personalQueue) {
        console.log('Personal queue not found:', queueId);
        return false;
      }

      const queueData = personalQueue.queueData;

      // Load the queue data
      setVideoQueue(queueData.queue.map(video => video.toObject()));
      setCurrentVideoIndex(queueData.currentIndex);
      setIsPlaying(false); // Don't auto-play

      // Update UI
      updateQueueDisplay();
      updatePlayerControls();
      saveQueueToStorage();

      console.log(`Loaded personal queue with ${queueData.queue.length} videos`);
      return true;

    } catch (error) {
      console.error('Error loading personal queue:', error);
      return false;
    }
  }

  /**
   * List personal queues for user
   * @param {string} userId - User ID
   * @returns {Promise<Array>} Array of personal queues
   */
  async listQueuesForUser(userId) {
    try {
      const queues = await this.repository.listForUser(userId);
      
      // Transform to legacy format
      return queues.map(queue => ({
        id: queue.id,
        title: queue.queueData.title,
        videoCount: queue.queueData.getVideoCount(),
        lastModified: queue.lastModified?.toDate?.() || new Date(),
        createdAt: queue.createdAt?.toDate?.() || new Date(),
        isPublic: queue.isPublic
      }));

    } catch (error) {
      console.error('Error listing personal queues:', error);
      return [];
    }
  }
}

// Global service instances
window.publicQueueService = new PublicQueueService();
window.personalQueueService = new PersonalQueueService();

console.log('✅ Firebase Services module loaded');
