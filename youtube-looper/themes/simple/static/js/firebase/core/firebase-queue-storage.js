// ============================================================================
// FIREBASE QUEUE STORAGE AND AUTO-SAVE FUNCTIONALITY
// ============================================================================

// Global variables for debouncing auto-save
let autoSaveTimeout = null;
const AUTO_SAVE_DELAY = 2000; // 2 seconds delay for auto-save

/**
 * Auto-save queue to Firebase personal queue
 * Debounced to avoid excessive Firebase writes
 * @param {boolean} immediate - If true, save immediately without debouncing
 */
function saveQueueToStorage(immediate = false) {
  if (immediate) {
    // Save immediately for critical operations like adding items
    console.log('🚀 Immediate Firebase sync triggered');

    // NOTE: Personal queue auto-save disabled to prevent unwanted queue creation
    // The current queue is a working queue, not a saved personal queue
    // Personal queues should only be created explicitly by the user

    // Only update shared queue if it exists (for sharing functionality)
    if (typeof autoUpdateSharedQueue === 'function') {
      autoUpdateSharedQueue().then(() => {
        console.log('✅ Shared queue immediately synced to Firebase');
      }).catch(error => {
        console.warn('❌ Could not immediately update shared queue:', error);
      });
    }
  } else {
    // NOTE: Debounced personal queue auto-save also disabled
    // Clear existing timeout if any
    if (autoSaveTimeout) {
      clearTimeout(autoSaveTimeout);
    }

    // Only update shared queue if it exists
    if (typeof autoUpdateSharedQueue === 'function') {
      autoSaveTimeout = setTimeout(async () => {
        try {
          await autoUpdateSharedQueue();
        } catch (error) {
          console.warn('Could not auto-update shared queue:', error);
        }
      }, AUTO_SAVE_DELAY);
    }
  }

  // Show storage indicator briefly
  const indicator = document.getElementById('storage-indicator');
  if (indicator && getVideoQueue().length > 0) {
    indicator.classList.remove('show');
    // Force reflow to restart animation
    indicator.offsetHeight;
    indicator.classList.add('show');
  }
}

/**
 * Load queue from Firebase using user's personal queue
 */
async function loadQueueFromStorage() {
  if (!isFirebaseInitialized()) {
    console.warn('Firebase not initialized, cannot load queue');
    return;
  }

  // Don't load personal queue if we're viewing a shared queue
  if (isViewingSharedQueue) {
    console.log('🔗 Skipping personal queue load - viewing shared queue');
    return;
  }

  // Get user ID from Firebase Auth
  const userId = await getUserId();
  if (!userId) {
    console.log('No user authenticated, starting with empty queue');
    return;
  }

  try {
    console.log('🔄 Loading personal queue from Firebase for user:', userId);
    const db = getFirebaseDb();
    const docRef = db.collection('personal_queues').doc(userId);
    const docSnapshot = await docRef.get();

    if (!docSnapshot.exists) {
      console.log('No personal queue found, starting with empty queue');
      return;
    }

    const data = docSnapshot.data();
    const loadedData = data.queueData;

    // Check if data is not too old (optional: expire after 7 days)
    const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds
    if (Date.now() - loadedData.timestamp > maxAge) {
      console.log('Personal queue data too old, starting with empty queue');
      return;
    }

    // Load the queue data
    setVideoQueue(loadedData.queue || []);
    setCurrentVideoIndex(loadedData.currentIndex || 0);
    setIsPlaying(false); // Don't auto-play on load

    // Ensure currentVideoIndex is within bounds
    if (getCurrentVideoIndex() >= getVideoQueue().length) {
      setCurrentVideoIndex(0);
    }

    console.log(`✅ Loaded ${getVideoQueue().length} video(s) from personal queue`);

    // Update UI
    updateQueueDisplay();
    updatePlayerControls();

  } catch (error) {
    console.warn('Could not load personal queue from Firebase:', error);
  }
}

/**
 * Auto-save personal queue to Firebase
 * NOTE: This function is no longer used for automatic queue saving to prevent
 * unwanted personal queue creation. It's kept for compatibility and explicit
 * personal queue operations (like switching modes).
 * @returns {Promise<boolean>} - True if successful
 */
async function autoSavePersonalQueue() {
  console.log('⚠️ autoSavePersonalQueue called - this function is disabled to prevent unwanted queue creation');
  console.log('💡 Current queue sync is handled by autoUpdateSharedQueue for sharing functionality');
  console.log('📝 Personal queues should be created explicitly via the queue creation interface');

  // Return true to avoid breaking existing code that expects this function to work
  return true;
}

/**
 * Clear personal queue from Firebase
 * NOTE: This function is mainly for cleaning up old-style personal queue documents
 * that may have been created before the auto-save was disabled.
 */
async function clearQueueFromStorage() {
  if (!isFirebaseInitialized()) {
    console.warn('Firebase not initialized, cannot clear personal queue');
    return;
  }

  // Get user ID from Firebase Auth
  const userId = await getUserId();
  if (!userId) {
    console.warn('No user authenticated, cannot clear personal queue');
    return;
  }

  try {
    const db = getFirebaseDb();
    const docRef = db.collection('personal_queues').doc(userId);

    // Check if the document exists before trying to delete it
    const docSnapshot = await docRef.get();
    if (docSnapshot.exists) {
      await docRef.delete();
      console.log('✅ Old-style personal queue cleared from Firebase for user:', userId);
    } else {
      console.log('ℹ️ No old-style personal queue found to clear for user:', userId);
    }
  } catch (error) {
    console.warn('Could not clear personal queue from Firebase:', error);
  }
}

/**
 * Check if there's a saved personal queue in Firebase
 * @returns {Promise<boolean>} - True if saved data exists
 */
async function hasSavedQueue() {
  if (!isFirebaseInitialized()) {
    return false;
  }

  // Get user ID from Firebase Auth
  const userId = await getUserId();
  if (!userId) {
    return false;
  }

  try {
    const db = getFirebaseDb();
    const docRef = db.collection('personal_queues').doc(userId);
    const docSnapshot = await docRef.get();
    return docSnapshot.exists;
  } catch (error) {
    console.warn('Could not check for saved personal queue:', error);
    return false;
  }
}

/**
 * Get saved personal queue data without loading it
 * @returns {Promise<Object|null>} - Saved queue data or null
 */
async function getSavedQueueData() {
  if (!isFirebaseInitialized()) {
    return null;
  }

  // Get user ID from Firebase Auth
  const userId = await getUserId();
  if (!userId) {
    return null;
  }

  try {
    const db = getFirebaseDb();
    const docRef = db.collection('personal_queues').doc(userId);
    const docSnapshot = await docRef.get();

    if (!docSnapshot.exists) {
      return null;
    }

    const data = docSnapshot.data();
    const queueData = data.queueData;

    // Check if data is not too old
    const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds
    if (Date.now() - queueData.timestamp > maxAge) {
      return null;
    }

    return queueData;
  } catch (error) {
    console.warn('Could not get saved personal queue data:', error);
    return null;
  }
}

console.log('✅ Firebase Queue Storage module loaded');
