// ============================================================================
// FIREBASE CONFIGURATION AND INITIALIZATION
// ============================================================================

// Global Firebase variables
let firebaseInitialized = false;
let firebaseConfig = null;
let currentUser = null;

/**
 * Load Firebase configuration from external file
 * @returns {Promise<boolean>} - True if config loaded successfully
 */
async function loadFirebaseConfig() {
  try {
    const response = await fetch('firebase-config.json');
    if (response.ok) {
      firebaseConfig = await response.json();
      console.log('✅ Firebase config loaded');
      return true;
    }
  } catch (error) {
    console.warn('⚠️ Firebase config file not found');
  }
  return false;
}

/**
 * Initialize Firebase when scripts are loaded
 * @returns {Promise<boolean>} - True if Firebase initialized successfully
 */
async function initializeFirebaseWhenReady() {
  // Wait for Firebase scripts to load
  let attempts = 0;
  const maxAttempts = 50;

  while (attempts < maxAttempts) {
    if (typeof firebase !== 'undefined' && firebaseConfig) {
      try {
        firebase.initializeApp(firebaseConfig);
        window.db = firebase.firestore();
        window.auth = firebase.auth();

        // Enable offline persistence for better UX
        try {
          await firebase.firestore().enablePersistence({
            synchronizeTabs: true
          });
          console.log('✅ Firebase offline persistence enabled');
        } catch (persistenceError) {
          if (persistenceError.code === 'failed-precondition') {
            console.warn('⚠️ Firebase persistence failed: Multiple tabs open');
          } else if (persistenceError.code === 'unimplemented') {
            console.warn('⚠️ Firebase persistence not supported in this browser');
          } else if (persistenceError.code === 'unavailable') {
            console.warn('⚠️ Firebase persistence unavailable: IndexedDB error. Falling back to memory cache.');
            console.warn('💡 Try clearing browser storage or using a different browser/incognito mode');
          } else {
            console.warn('⚠️ Firebase persistence error:', persistenceError);
          }
          // Continue without persistence - app will still work with memory cache
          console.log('📱 App will continue with memory-only cache (no offline support)');
        }

        // Set up auth state listener
        firebase.auth().onAuthStateChanged(async (user) => {
          currentUser = user;
          if (user) {
            console.log('🔐 User authenticated:', user.uid, user.isAnonymous ? '(anonymous)' : '(Google)');
            dispatchAuthStateChange(true, user);
          } else {
            console.log('🔓 User not authenticated - signing in anonymously...');
            // Automatically sign in anonymously for public queue access
            try {
              const anonymousUser = await firebase.auth().signInAnonymously();
              console.log('🔐 Anonymous sign-in successful:', anonymousUser.user.uid);
            } catch (error) {
              console.error('❌ Anonymous sign-in failed:', error);
              dispatchAuthStateChange(false, null);
            }
          }
        });

        firebaseInitialized = true;
        console.log('🔥 Firebase initialized successfully');
        return true;
      } catch (error) {
        console.error('❌ Error initializing Firebase:', error);
        return false;
      }
    }
    await new Promise(resolve => setTimeout(resolve, 100));
    attempts++;
  }

  console.warn('⚠️ Firebase initialization timeout');
  return false;
}

/**
 * Load Firebase scripts dynamically
 */
function loadFirebaseScripts() {
  // Load Firebase App script
  const firebaseAppScript = document.createElement('script');
  firebaseAppScript.src = "https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js";
  document.head.appendChild(firebaseAppScript);

  // Load Firebase Firestore script
  const firebaseFirestoreScript = document.createElement('script');
  firebaseFirestoreScript.src = "https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore-compat.js";
  document.head.appendChild(firebaseFirestoreScript);

  // Load Firebase Auth script
  const firebaseAuthScript = document.createElement('script');
  firebaseAuthScript.src = "https://www.gstatic.com/firebasejs/9.23.0/firebase-auth-compat.js";
  document.head.appendChild(firebaseAuthScript);

  console.log('📦 Firebase scripts loading...');
}

/**
 * Check if Firebase is initialized
 * @returns {boolean} - True if Firebase is initialized
 */
function isFirebaseInitialized() {
  return firebaseInitialized;
}

/**
 * Get Firebase database instance
 * @returns {Object|null} - Firebase database instance or null
 */
function getFirebaseDb() {
  return window.db || null;
}

/**
 * Get Firebase auth instance
 * @returns {Object|null} - Firebase auth instance or null
 */
function getFirebaseAuth() {
  return window.auth || null;
}

/**
 * Get current authenticated user
 * @returns {Object|null} - Current user or null
 */
function getCurrentUser() {
  return currentUser;
}

/**
 * Sign in with Google
 * @returns {Promise<Object|null>} - User object or null
 */
async function signInWithGoogle() {
  if (!isFirebaseInitialized()) {
    console.warn('Firebase not initialized');
    return null;
  }

  try {
    const auth = getFirebaseAuth();
    const provider = new firebase.auth.GoogleAuthProvider();

    // Add scopes for profile information
    provider.addScope('profile');
    provider.addScope('email');

    const result = await auth.signInWithPopup(provider);
    console.log('🔐 Google sign-in successful:', result.user.uid);

    // Dispatch auth state change event
    dispatchAuthStateChange(true, result.user);

    return result.user;
  } catch (error) {
    console.error('❌ Google sign-in failed:', error);

    // Handle specific error cases
    if (error.code === 'auth/popup-closed-by-user') {
      console.log('Sign-in popup was closed by user');
    } else if (error.code === 'auth/popup-blocked') {
      console.error('Sign-in popup was blocked by browser');
    }

    return null;
  }
}

/**
 * Sign in with email and password
 * @param {string} email - User email
 * @param {string} password - User password
 * @returns {Promise<Object|null>} - User object or null
 */
async function signInWithEmail(email, password) {
  if (!isFirebaseInitialized()) {
    console.warn('Firebase not initialized');
    return null;
  }

  try {
    const auth = getFirebaseAuth();
    const result = await auth.signInWithEmailAndPassword(email, password);
    console.log('🔐 Email sign-in successful:', result.user.uid);

    // Dispatch auth state change event
    dispatchAuthStateChange(true, result.user);

    return result.user;
  } catch (error) {
    console.error('❌ Email sign-in failed:', error);
    throw error; // Re-throw to handle specific error codes in UI
  }
}

/**
 * Create account with email and password
 * @param {string} email - User email
 * @param {string} password - User password
 * @param {string} displayName - User display name (optional)
 * @returns {Promise<Object|null>} - User object or null
 */
async function createAccountWithEmail(email, password, displayName = null) {
  if (!isFirebaseInitialized()) {
    console.warn('Firebase not initialized');
    return null;
  }

  try {
    const auth = getFirebaseAuth();
    const result = await auth.createUserWithEmailAndPassword(email, password);

    // Update profile with display name if provided
    if (displayName && result.user) {
      await result.user.updateProfile({
        displayName: displayName
      });
    }

    console.log('🔐 Account creation successful:', result.user.uid);

    // Dispatch auth state change event
    dispatchAuthStateChange(true, result.user);

    return result.user;
  } catch (error) {
    console.error('❌ Account creation failed:', error);
    throw error; // Re-throw to handle specific error codes in UI
  }
}

/**
 * Send password reset email
 * @param {string} email - User email
 * @returns {Promise<boolean>} - True if email sent successfully
 */
async function sendPasswordResetEmail(email) {
  if (!isFirebaseInitialized()) {
    console.warn('Firebase not initialized');
    return false;
  }

  try {
    const auth = getFirebaseAuth();
    await auth.sendPasswordResetEmail(email);
    console.log('📧 Password reset email sent to:', email);
    return true;
  } catch (error) {
    console.error('❌ Password reset email failed:', error);
    throw error; // Re-throw to handle specific error codes in UI
  }
}

/**
 * Sign in anonymously
 * @returns {Promise<Object|null>} - User object or null
 */
async function signInAnonymously() {
  if (!isFirebaseInitialized()) {
    console.warn('Firebase not initialized');
    return null;
  }

  try {
    const auth = getFirebaseAuth();
    const result = await auth.signInAnonymously();
    console.log('🔐 Anonymous sign-in successful:', result.user.uid);

    // Dispatch auth state change event
    dispatchAuthStateChange(true, result.user);

    return result.user;
  } catch (error) {
    console.error('❌ Anonymous sign-in failed:', error);
    return null;
  }
}

/**
 * Sign out current user
 * @returns {Promise<boolean>} - True if sign-out successful
 */
async function signOut() {
  if (!isFirebaseInitialized()) {
    console.warn('Firebase not initialized');
    return false;
  }

  try {
    const auth = getFirebaseAuth();
    await auth.signOut();
    console.log('🔓 Sign-out successful');

    // Dispatch auth state change event
    dispatchAuthStateChange(false, null);

    return true;
  } catch (error) {
    console.error('❌ Sign-out failed:', error);
    return false;
  }
}



/**
 * Dispatch authentication state change event
 * @param {boolean} isAuthenticated - Whether user is authenticated
 * @param {Object|null} user - User object or null
 */
function dispatchAuthStateChange(isAuthenticated, user) {
  const event = new CustomEvent('authStateChanged', {
    detail: {
      isAuthenticated,
      user,
      userId: user ? user.uid : null,
      userEmail: user ? user.email : null,
      userName: user ? user.displayName : null,
      userPhoto: user ? user.photoURL : null
    }
  });
  document.dispatchEvent(event);
}

/**
 * Get user ID for queue storage (requires Google authentication)
 * @returns {Promise<string|null>} - User ID or null
 */
async function getUserId() {
  const user = getCurrentUser();

  if (!user) {
    console.log('No authenticated user - Google sign-in required');
    return null;
  }

  // Only allow Google authenticated users (not anonymous)
  if (user.isAnonymous) {
    console.log('Anonymous user detected - Google sign-in required');
    return null;
  }

  return user.uid;
}

/**
 * Check if current user is authenticated (including anonymous)
 * @returns {boolean} - True if user is authenticated
 */
function isUserAuthenticated() {
  const user = getCurrentUser();
  return user !== null;
}

/**
 * Check if current user is authenticated with Google
 * @returns {boolean} - True if user is authenticated with Google
 */
function isGoogleAuthenticated() {
  const user = getCurrentUser();
  return user && !user.isAnonymous;
}

/**
 * Get user display information
 * @returns {Object|null} - User display info or null
 */
function getUserDisplayInfo() {
  const user = getCurrentUser();
  if (!user) return null;

  return {
    uid: user.uid,
    email: user.email,
    displayName: user.displayName,
    photoURL: user.photoURL,
    isAnonymous: user.isAnonymous
  };
}

/**
 * Alias for getUserId() for backward compatibility
 * @returns {Promise<string|null>} - User ID or null
 */
async function getCurrentUserId() {
  return await getUserId();
}

/**
 * Initialize Firebase module
 * @returns {Promise<boolean>} - True if initialization successful
 */
async function initializeFirebase() {
  console.log('🔥 Initializing Firebase...');
  
  // Load Firebase scripts
  loadFirebaseScripts();
  
  // Load Firebase configuration
  const configLoaded = await loadFirebaseConfig();
  if (configLoaded) {
    await initializeFirebaseWhenReady();
    return true;
  } else {
    console.warn('⚠️ Firebase configuration not found. Queue sharing features will be disabled.');
    return false;
  }
}

console.log('✅ Firebase module loaded');
