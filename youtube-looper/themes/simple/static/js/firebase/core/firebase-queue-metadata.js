// ============================================================================
// FIREBASE QUEUE METADATA UTILITIES
// ============================================================================

/**
 * Extract metadata from queue data for storage and display
 * @param {Object} queueData - Queue data object
 * @returns {Object} - Extracted metadata
 */
function extractQueueMetadata(queueData) {
  const queue = queueData.queue || [];
  
  if (queue.length === 0) {
    return {
      title: queueData.title || 'Empty Queue',
      videoCount: 0,
      totalDuration: 0,
      firstVideoThumbnail: ''
    };
  }

  // Calculate total duration
  let totalDuration = 0;
  queue.forEach(video => {
    if (video.duration) {
      // Parse duration string (e.g., "3:45" -> 225 seconds)
      const parts = video.duration.split(':');
      if (parts.length === 2) {
        totalDuration += parseInt(parts[0]) * 60 + parseInt(parts[1]);
      } else if (parts.length === 3) {
        totalDuration += parseInt(parts[0]) * 3600 + parseInt(parts[1]) * 60 + parseInt(parts[2]);
      }
    }
  });

  return {
    title: queueData.title || `Queue with ${queue.length} video${queue.length !== 1 ? 's' : ''}`,
    videoCount: queue.length,
    totalDuration: totalDuration,
    firstVideoThumbnail: queue[0]?.thumbnail || ''
  };
}

/**
 * Format duration in seconds to human readable string
 * @param {number} seconds - Duration in seconds
 * @returns {string} - Formatted duration string
 */
function formatDuration(seconds) {
  if (seconds < 60) {
    return `${seconds}s`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
  }
}

/**
 * Generate a unique queue ID
 * @returns {string} - Unique queue ID
 */
function generateQueueId() {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `queue_${timestamp}_${random}`;
}

console.log('✅ Firebase Queue Metadata module loaded');
