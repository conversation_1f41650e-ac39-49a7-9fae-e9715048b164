// ============================================================================
// FIREBASE REPOSITORY CLASSES
// ============================================================================

/**
 * Base repository class for Firebase operations
 */
class FirebaseRepository {
  constructor(collectionName) {
    this.collectionName = collectionName;
  }

  /**
   * Get Firebase database instance
   * @returns {Object} Firebase database instance
   */
  getDb() {
    return window.db;
  }

  /**
   * Get collection reference
   * @returns {Object} Firebase collection reference
   */
  getCollection() {
    return this.getDb().collection(this.collectionName);
  }

  /**
   * Get document reference
   * @param {string} docId - Document ID
   * @returns {Object} Firebase document reference
   */
  getDoc(docId) {
    return this.getCollection().doc(docId);
  }
}

/**
 * Repository for public shared queues
 */
class PublicQueueRepository extends FirebaseRepository {
  constructor() {
    super(PublicQueueModel.COLLECTION);
  }

  /**
   * Save a public queue
   * @param {PublicQueueModel} queueModel - Queue model to save
   * @returns {Promise<string>} Queue ID
   */
  async save(queueModel) {
    const docRef = queueModel.id ?
      this.getDoc(queueModel.id) :
      this.getCollection().doc();

    const data = queueModel.toFirebaseData();

    // Ensure metadata exists
    if (!data.metadata) {
      data.metadata = {};
    }

    // Add server timestamp to metadata
    data.metadata[QueueMetadataModel.FIELDS.LAST_MODIFIED] = firebase.firestore.FieldValue.serverTimestamp();

    // Set creation timestamp if new
    if (!queueModel.id) {
      data.metadata[QueueMetadataModel.FIELDS.CREATED_AT] = firebase.firestore.FieldValue.serverTimestamp();
      queueModel.id = docRef.id;
    }

    console.log('💾 Saving public queue with data structure:', {
      id: queueModel.id,
      hasQueueData: !!data.queueData,
      hasMetadata: !!data.metadata,
      metadataFields: data.metadata ? Object.keys(data.metadata) : []
    });

    await docRef.set(data);
    queueModel.markSaved();

    return docRef.id;
  }

  /**
   * Load a public queue by ID
   * @param {string} queueId - Queue ID
   * @returns {Promise<PublicQueueModel|null>} Queue model or null
   */
  async load(queueId) {
    const docSnapshot = await this.getDoc(queueId).get();
    
    if (!docSnapshot.exists) {
      return null;
    }

    return PublicQueueModel.fromFirebaseDoc(docSnapshot);
  }

  /**
   * Delete a public queue
   * @param {string} queueId - Queue ID
   * @returns {Promise<void>}
   */
  async delete(queueId) {
    await this.getDoc(queueId).delete();
  }

  /**
   * List public queues with sorting and pagination
   * @param {Object} options - Query options
   * @returns {Promise<Array<PublicQueueModel>>} Array of queue models
   */
  async list(options = {}) {
    const {
      sortBy = 'recent',
      limit = 50,
      startAfter = null
    } = options;

    console.log('🔍 PublicQueueRepository.list() called with options:', { sortBy, limit, startAfter });

    let query = this.getCollection();
    let orderByField;

    // Apply sorting
    switch (sortBy) {
      case 'popular':
        orderByField = `metadata.${QueueMetadataModel.FIELDS.VIEW_COUNT}`;
        query = query.orderBy(orderByField, 'desc');
        break;
      case 'recent':
        orderByField = `metadata.${QueueMetadataModel.FIELDS.LAST_MODIFIED}`;
        query = query.orderBy(orderByField, 'desc');
        break;
      case 'newest':
        orderByField = `metadata.${QueueMetadataModel.FIELDS.CREATED_AT}`;
        query = query.orderBy(orderByField, 'desc');
        break;
      case 'longest':
        orderByField = `metadata.${QueueMetadataModel.FIELDS.TOTAL_DURATION}`;
        query = query.orderBy(orderByField, 'desc');
        break;
      default:
        orderByField = `metadata.${QueueMetadataModel.FIELDS.LAST_MODIFIED}`;
        query = query.orderBy(orderByField, 'desc');
    }

    console.log('📊 Query orderBy field:', orderByField);

    // Apply pagination
    if (startAfter) {
      query = query.startAfter(startAfter);
    }

    query = query.limit(limit);

    try {
      console.log('🔥 Executing Firebase query...');
      const querySnapshot = await query.get();
      console.log('📦 Firebase query returned:', querySnapshot.size, 'documents');

      const queues = [];

      querySnapshot.forEach((doc) => {
        console.log('📄 Processing document:', doc.id);
        const docData = doc.data();
        console.log('📄 Document data structure:', {
          hasQueueData: !!docData.queueData,
          hasMetadata: !!docData.metadata,
          rootLevelFields: Object.keys(docData),
          metadataFields: docData.metadata ? Object.keys(docData.metadata) : []
        });

        try {
          const queue = PublicQueueModel.fromFirebaseDoc(doc);
          queues.push(queue);
          console.log('✅ Successfully created queue model for:', doc.id);
        } catch (error) {
          console.error('❌ Error creating queue model for document:', doc.id, error);
        }
      });

      console.log('🎯 Final queues array length:', queues.length);
      return queues;

    } catch (error) {
      console.error('❌ Firebase query error:', error);

      // Fallback: try without orderBy if the field doesn't exist
      console.log('🔄 Attempting fallback query without orderBy...');
      try {
        const fallbackQuery = this.getCollection().limit(limit);
        const fallbackSnapshot = await fallbackQuery.get();
        console.log('📦 Fallback query returned:', fallbackSnapshot.size, 'documents');

        const queues = [];
        fallbackSnapshot.forEach((doc) => {
          try {
            const queue = PublicQueueModel.fromFirebaseDoc(doc);
            queues.push(queue);
          } catch (error) {
            console.error('❌ Error creating queue model in fallback for document:', doc.id, error);
          }
        });

        console.log('🎯 Fallback queues array length:', queues.length);
        return queues;

      } catch (fallbackError) {
        console.error('❌ Fallback query also failed:', fallbackError);
        throw error; // Re-throw original error
      }
    }
  }

  /**
   * Increment view count for a queue
   * @param {string} queueId - Queue ID
   * @returns {Promise<void>}
   */
  async incrementViewCount(queueId) {
    await this.getDoc(queueId).update({
      [`${QueueMetadataModel.FIELDS.VIEW_COUNT}`]: firebase.firestore.FieldValue.increment(1)
    });
  }

  /**
   * Update queue metadata only
   * @param {string} queueId - Queue ID
   * @param {QueueMetadataModel} metadata - Metadata to update
   * @returns {Promise<void>}
   */
  async updateMetadata(queueId, metadata) {
    const updateData = {};
    updateData[`${QueueMetadataModel.FIELDS.LAST_MODIFIED}`] = firebase.firestore.FieldValue.serverTimestamp();
    
    // Add metadata fields with proper paths
    Object.keys(metadata.toObject()).forEach(key => {
      if (key !== QueueMetadataModel.FIELDS.CREATED_AT) { // Don't update creation date
        updateData[`metadata.${key}`] = metadata.get(key);
      }
    });

    await this.getDoc(queueId).update(updateData);
  }
}

/**
 * Repository for personal queues
 */
class PersonalQueueRepository extends FirebaseRepository {
  constructor() {
    super(PersonalQueueModel.COLLECTION);
  }

  /**
   * Save a personal queue
   * @param {PersonalQueueModel} queueModel - Queue model to save
   * @returns {Promise<string>} Queue ID
   */
  async save(queueModel) {
    const docRef = queueModel.id ? 
      this.getDoc(queueModel.id) : 
      this.getCollection().doc();
    
    const data = queueModel.toFirebaseData();
    
    // Add server timestamp
    data[PersonalQueueModel.FIELDS.LAST_MODIFIED] = firebase.firestore.FieldValue.serverTimestamp();
    
    // Set creation timestamp if new
    if (!queueModel.id) {
      data[PersonalQueueModel.FIELDS.CREATED_AT] = firebase.firestore.FieldValue.serverTimestamp();
      queueModel.id = docRef.id;
    }

    await docRef.set(data);
    queueModel.markSaved();
    
    return docRef.id;
  }

  /**
   * Load a personal queue by ID
   * @param {string} queueId - Queue ID
   * @returns {Promise<PersonalQueueModel|null>} Queue model or null
   */
  async load(queueId) {
    const docSnapshot = await this.getDoc(queueId).get();
    
    if (!docSnapshot.exists) {
      return null;
    }

    return PersonalQueueModel.fromFirebaseDoc(docSnapshot);
  }

  /**
   * Load personal queue for user (legacy single queue per user)
   * @param {string} userId - User ID
   * @returns {Promise<PersonalQueueModel|null>} Queue model or null
   */
  async loadForUser(userId) {
    const docSnapshot = await this.getDoc(userId).get();
    
    if (!docSnapshot.exists) {
      return null;
    }

    return PersonalQueueModel.fromFirebaseDoc(docSnapshot);
  }

  /**
   * List personal queues for a user
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @returns {Promise<Array<PersonalQueueModel>>} Array of queue models
   */
  async listForUser(userId, options = {}) {
    const {
      limit = 50,
      orderBy = PersonalQueueModel.FIELDS.LAST_MODIFIED,
      direction = 'desc'
    } = options;

    let query = this.getCollection()
      .where(PersonalQueueModel.FIELDS.USER_ID, '==', userId)
      .orderBy(orderBy, direction)
      .limit(limit);

    const querySnapshot = await query.get();
    const queues = [];

    querySnapshot.forEach((doc) => {
      queues.push(PersonalQueueModel.fromFirebaseDoc(doc));
    });

    return queues;
  }

  /**
   * Delete a personal queue
   * @param {string} queueId - Queue ID
   * @returns {Promise<void>}
   */
  async delete(queueId) {
    await this.getDoc(queueId).delete();
  }

  /**
   * Save personal queue for user (legacy single queue per user)
   * @param {string} userId - User ID
   * @param {QueueDataModel} queueData - Queue data
   * @returns {Promise<void>}
   */
  async saveForUser(userId, queueData) {
    const data = {
      [PersonalQueueModel.FIELDS.QUEUE_DATA]: queueData.toObject(),
      [PersonalQueueModel.FIELDS.LAST_MODIFIED]: firebase.firestore.FieldValue.serverTimestamp()
    };

    await this.getDoc(userId).set(data);
  }
}

// Global repository instances
window.publicQueueRepo = new PublicQueueRepository();
window.personalQueueRepo = new PersonalQueueRepository();

console.log('✅ Firebase Repositories module loaded');
