// ============================================================================
// PERSONAL QUEUE MODEL CLASS
// ============================================================================

/**
 * Personal queue model (personal_queues collection)
 * Represents personal queues that belong to specific users
 */
class PersonalQueueModel extends FirebaseModel {
  static COLLECTION = 'personal_queues';

  static FIELDS = {
    ID: 'id',
    USER_ID: 'userId',
    QUEUE_DATA: 'queueData',
    LAST_MODIFIED: 'lastModified',
    CREATED_AT: 'createdAt',
    IS_PERSONAL: 'isPersonal',
    IS_PUBLIC: 'isPublic'
  };

  constructor(data = {}) {
    super(data);
  }

  // Getters
  get id() { return this.get(PersonalQueueModel.FIELDS.ID); }
  get userId() { return this.get(PersonalQueueModel.FIELDS.USER_ID); }
  get queueData() {
    const data = this.get(PersonalQueueModel.FIELDS.QUEUE_DATA);
    return data ? QueueDataModel.fromObject(data) : new QueueDataModel();
  }
  get lastModified() { return this.get(PersonalQueueModel.FIELDS.LAST_MODIFIED); }
  get createdAt() { return this.get(PersonalQueueModel.FIELDS.CREATED_AT); }
  get isPersonal() { return this.get(PersonalQueueModel.FIELDS.IS_PERSONAL) !== false; }
  get isPublic() { return this.get(PersonalQueueModel.FIELDS.IS_PUBLIC) === true; }

  // Setters
  set id(value) { this.set(PersonalQueueModel.FIELDS.ID, value); }
  set userId(value) { this.set(PersonalQueueModel.FIELDS.USER_ID, value); }
  set queueData(value) {
    const data = value instanceof QueueDataModel ? value.toObject() : value;
    this.set(PersonalQueueModel.FIELDS.QUEUE_DATA, data);
  }
  set lastModified(value) { this.set(PersonalQueueModel.FIELDS.LAST_MODIFIED, value); }
  set createdAt(value) { this.set(PersonalQueueModel.FIELDS.CREATED_AT, value); }
  set isPersonal(value) { this.set(PersonalQueueModel.FIELDS.IS_PERSONAL, value); }
  set isPublic(value) { this.set(PersonalQueueModel.FIELDS.IS_PUBLIC, value); }

  /**
   * Get queue title
   * @returns {string} Queue title
   */
  getTitle() {
    return this.queueData.title || 'Untitled Personal Queue';
  }

  /**
   * Get video count
   * @returns {number} Number of videos
   */
  getVideoCount() {
    return this.queueData.getVideoCount();
  }

  /**
   * Get total duration
   * @returns {number} Total duration in seconds
   */
  getTotalDuration() {
    return this.queueData.getTotalDuration();
  }

  /**
   * Get formatted total duration
   * @returns {string} Formatted duration
   */
  getFormattedTotalDuration() {
    return this.queueData.getFormattedTotalDuration();
  }

  /**
   * Get first video thumbnail
   * @returns {string} Thumbnail URL
   */
  getThumbnail() {
    return this.queueData.getFirstVideoThumbnail();
  }

  /**
   * Get creation date
   * @returns {Date|null} Creation date
   */
  getCreatedAt() {
    if (!this.createdAt) return null;
    return this.createdAt.toDate ? this.createdAt.toDate() : new Date(this.createdAt);
  }

  /**
   * Get last modified date
   * @returns {Date|null} Last modified date
   */
  getLastModified() {
    if (!this.lastModified) return null;
    return this.lastModified.toDate ? this.lastModified.toDate() : new Date(this.lastModified);
  }

  /**
   * Get relative time since last modified
   * @returns {string} Relative time
   */
  getRelativeLastModified() {
    const date = this.getLastModified();
    if (!date) return 'Unknown';
    
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minute${diffMins === 1 ? '' : 's'} ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours === 1 ? '' : 's'} ago`;
    if (diffDays < 7) return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`;

    return date.toLocaleDateString();
  }

  /**
   * Check if queue is empty
   * @returns {boolean} True if queue has no videos
   */
  isEmpty() {
    return this.getVideoCount() === 0;
  }

  /**
   * Check if queue belongs to user
   * @param {string} userId - User ID to check
   * @returns {boolean} True if queue belongs to user
   */
  belongsToUser(userId) {
    return this.userId === userId;
  }

  /**
   * Toggle public/private status
   */
  togglePublic() {
    this.isPublic = !this.isPublic;
  }

  /**
   * Update last modified timestamp
   */
  updateLastModified() {
    this.lastModified = new Date();
  }

  /**
   * Generate unique queue ID for user
   * @param {string} userId - User ID
   * @returns {string} Generated queue ID
   */
  static generateQueueId(userId) {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `personal_${userId}_${timestamp}_${random}`;
  }

  /**
   * Get display data for UI
   * @returns {Object} Display data
   */
  getDisplayData() {
    return {
      id: this.id,
      title: this.getTitle(),
      videoCount: this.getVideoCount(),
      totalDuration: this.getTotalDuration(),
      formattedDuration: this.getFormattedTotalDuration(),
      thumbnail: this.getThumbnail(),
      createdAt: this.getCreatedAt(),
      lastModified: this.getLastModified(),
      relativeTime: this.getRelativeLastModified(),
      isPublic: this.isPublic,
      isPersonal: this.isPersonal,
      userId: this.userId,
      isEmpty: this.isEmpty()
    };
  }

  /**
   * Create PersonalQueueModel from plain object
   * @param {Object} data - Queue document data
   * @returns {PersonalQueueModel} Personal queue model instance
   */
  static fromObject(data) {
    return new PersonalQueueModel(data);
  }

  /**
   * Create PersonalQueueModel from Firebase document
   * @param {Object} doc - Firebase document snapshot
   * @returns {PersonalQueueModel} Personal queue model instance
   */
  static fromFirebaseDoc(doc) {
    const data = doc.data();
    data[PersonalQueueModel.FIELDS.ID] = doc.id;
    return PersonalQueueModel.fromObject(data);
  }

  /**
   * Create PersonalQueueModel from current application state
   * @param {string} userId - User ID
   * @param {string} title - Queue title
   * @returns {PersonalQueueModel} Personal queue model instance
   */
  static fromCurrentState(userId, title = 'My Personal Queue') {
    const queueData = QueueDataModel.fromCurrentState(title);
    const queueId = PersonalQueueModel.generateQueueId(userId);
    
    return new PersonalQueueModel({
      [PersonalQueueModel.FIELDS.ID]: queueId,
      [PersonalQueueModel.FIELDS.USER_ID]: userId,
      [PersonalQueueModel.FIELDS.QUEUE_DATA]: queueData.toObject(),
      [PersonalQueueModel.FIELDS.IS_PERSONAL]: true,
      [PersonalQueueModel.FIELDS.IS_PUBLIC]: false
    });
  }

  /**
   * Create PersonalQueueModel from draft queue
   * @param {string} userId - User ID
   * @param {string} title - Queue title
   * @param {Array} draftQueue - Draft queue videos
   * @returns {PersonalQueueModel} Personal queue model instance
   */
  static fromDraftQueue(userId, title, draftQueue) {
    const queueData = new QueueDataModel({
      [QueueDataModel.FIELDS.QUEUE]: draftQueue,
      [QueueDataModel.FIELDS.CURRENT_INDEX]: 0,
      [QueueDataModel.FIELDS.IS_PLAYING]: false,
      [QueueDataModel.FIELDS.TIMESTAMP]: Date.now(),
      [QueueDataModel.FIELDS.TITLE]: title
    });
    
    const queueId = PersonalQueueModel.generateQueueId(userId);
    
    return new PersonalQueueModel({
      [PersonalQueueModel.FIELDS.ID]: queueId,
      [PersonalQueueModel.FIELDS.USER_ID]: userId,
      [PersonalQueueModel.FIELDS.QUEUE_DATA]: queueData.toObject(),
      [PersonalQueueModel.FIELDS.IS_PERSONAL]: true,
      [PersonalQueueModel.FIELDS.IS_PUBLIC]: false
    });
  }

  /**
   * Prepare data for Firebase save
   * @returns {Object} Data ready for Firebase
   */
  toFirebaseData() {
    const data = this.toObject();
    // Remove id from data as it's used as document ID
    delete data[PersonalQueueModel.FIELDS.ID];
    return data;
  }

  /**
   * Validate personal queue data
   * @param {Object} data - Queue data to validate
   * @returns {Object} Validation result with isValid and errors
   */
  static validate(data) {
    const errors = [];
    
    if (!data[PersonalQueueModel.FIELDS.USER_ID]) {
      errors.push('User ID is required');
    }
    
    // Validate queue data
    const queueData = data[PersonalQueueModel.FIELDS.QUEUE_DATA];
    if (!queueData) {
      errors.push('Queue data is required');
    } else {
      const queueValidation = QueueDataModel.validate(queueData);
      if (!queueValidation.isValid) {
        errors.push(...queueValidation.errors.map(err => `Queue data: ${err}`));
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

console.log('✅ Personal Queue Model loaded');
