// ============================================================================
// FIREBASE BASE MODEL CLASS
// ============================================================================

/**
 * Base model class for Firebase documents
 * Provides common functionality for all Firebase model classes
 */
class FirebaseModel {
  constructor(data = {}) {
    this._data = { ...data };
    this._originalData = { ...data };
  }

  /**
   * Get a field value
   * @param {string} field - Field name
   * @returns {*} Field value
   */
  get(field) {
    return this._data[field];
  }

  /**
   * Set a field value
   * @param {string} field - Field name
   * @param {*} value - Field value
   */
  set(field, value) {
    this._data[field] = value;
  }

  /**
   * Get all data as plain object
   * @returns {Object} Data object
   */
  toObject() {
    return { ...this._data };
  }

  /**
   * Check if model has been modified
   * @returns {boolean} True if modified
   */
  isDirty() {
    return JSON.stringify(this._data) !== JSON.stringify(this._originalData);
  }

  /**
   * Reset to original state
   */
  reset() {
    this._data = { ...this._originalData };
  }

  /**
   * Mark as saved (update original data)
   */
  markSaved() {
    this._originalData = { ...this._data };
  }
}

console.log('✅ Firebase Base Model loaded');
