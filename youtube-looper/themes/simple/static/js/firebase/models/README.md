# Firebase Models

This directory contains all Firebase model classes for the YouTube Looper application. Each model is in its own file for better maintainability and readability.

## File Structure

```
models/
├── firebase-model-base.js      # Base model class
├── video-model.js              # Individual video model
├── queue-data-model.js         # Queue content and state
├── queue-metadata-model.js     # Queue metadata for querying
├── public-queue-model.js       # Public shared queues
├── personal-queue-model.js     # Personal user queues
├── index.js                    # Model loader and validator
└── README.md                   # This file
```

## Loading Order

The models must be loaded in dependency order:

1. **firebase-model-base.js** - Base class for all models
2. **video-model.js** - Used by QueueDataModel
3. **queue-data-model.js** - Used by QueueMetadataModel and queue models
4. **queue-metadata-model.js** - Used by PublicQueueModel
5. **public-queue-model.js** - Collection model for public queues
6. **personal-queue-model.js** - Collection model for personal queues
7. **index.js** - Validation and testing utilities

## Model Classes

### FirebaseModel (Base Class)
- **File**: `firebase-model-base.js`
- **Purpose**: Base class providing common functionality for all Firebase models
- **Key Methods**: `get()`, `set()`, `toObject()`, `isDirty()`, `reset()`, `markSaved()`

### VideoModel
- **File**: `video-model.js`
- **Purpose**: Represents individual videos with metadata
- **Fields**: ID, title, thumbnail, duration, channel, description, publishedAt, viewCount
- **Key Methods**: `getFormattedDuration()`, `isValid()`, `getVideoUrl()`, `getThumbnailUrl()`

### QueueDataModel
- **File**: `queue-data-model.js`
- **Purpose**: Represents queue content and playback state
- **Fields**: queue, currentIndex, isPlaying, timestamp, title
- **Key Methods**: `getVideoCount()`, `getTotalDuration()`, `addVideo()`, `removeVideo()`, `moveVideo()`

### QueueMetadataModel
- **File**: `queue-metadata-model.js`
- **Purpose**: Represents metadata for efficient querying and display
- **Fields**: title, videoCount, totalDuration, firstVideoThumbnail, createdAt, lastModified, viewCount
- **Key Methods**: `getFormattedTotalDuration()`, `getRelativeLastModified()`, `isPopular()`, `isRecent()`

### PublicQueueModel
- **File**: `public-queue-model.js`
- **Purpose**: Represents public shared queues in the `queues` collection
- **Fields**: id, queueData, metadata
- **Key Methods**: `getTitle()`, `getVideoCount()`, `getPreviewData()`, `getShareableUrl()`

### PersonalQueueModel
- **File**: `personal-queue-model.js`
- **Purpose**: Represents personal user queues in the `personal_queues` collection
- **Fields**: id, userId, queueData, lastModified, createdAt, isPersonal, isPublic
- **Key Methods**: `getTitle()`, `belongsToUser()`, `togglePublic()`, `getDisplayData()`

## Usage Examples

### Creating a Video
```javascript
const video = new VideoModel({
  [VideoModel.FIELDS.ID]: 'dQw4w9WgXcQ',
  [VideoModel.FIELDS.TITLE]: 'Rick Astley - Never Gonna Give You Up',
  [VideoModel.FIELDS.DURATION]: 212
});

console.log(video.getFormattedDuration()); // "3:32"
console.log(video.getVideoUrl()); // "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
```

### Creating Queue Data
```javascript
const queueData = new QueueDataModel({
  [QueueDataModel.FIELDS.QUEUE]: [video.toObject()],
  [QueueDataModel.FIELDS.TITLE]: 'My Playlist',
  [QueueDataModel.FIELDS.CURRENT_INDEX]: 0
});

console.log(queueData.getVideoCount()); // 1
console.log(queueData.getTotalDuration()); // 212
```

### Creating Metadata
```javascript
const metadata = QueueMetadataModel.fromQueueData(queueData);
console.log(metadata.videoCount); // 1
console.log(metadata.getFormattedTotalDuration()); // "3:32"
```

### Creating Public Queue
```javascript
const publicQueue = new PublicQueueModel({
  [PublicQueueModel.FIELDS.ID]: 'queue123',
  [PublicQueueModel.FIELDS.QUEUE_DATA]: queueData.toObject(),
  [PublicQueueModel.FIELDS.METADATA]: metadata.toObject()
});

console.log(publicQueue.getPreviewData());
```

### Creating Personal Queue
```javascript
const personalQueue = PersonalQueueModel.fromCurrentState('user123', 'My Personal Playlist');
console.log(personalQueue.getDisplayData());
```

## Field Constants

All field names are defined as constants to prevent typos:

```javascript
// Video fields
VideoModel.FIELDS.ID           // 'id'
VideoModel.FIELDS.TITLE        // 'title'
VideoModel.FIELDS.DURATION     // 'duration'

// Queue data fields
QueueDataModel.FIELDS.QUEUE           // 'queue'
QueueDataModel.FIELDS.CURRENT_INDEX   // 'currentIndex'
QueueDataModel.FIELDS.IS_PLAYING      // 'isPlaying'

// And so on for all models...
```

## Validation and Testing

The `index.js` file provides utilities for validation and testing:

```javascript
// Check if all models are loaded
const validation = validateModelsLoaded();
console.log(validation.allLoaded); // true/false

// Get model information
const info = getModelInfo();
console.log(info.models);

// Run model tests
const testResults = testModels();
console.log(testResults.success); // true/false
```

## Benefits of Separate Files

1. **Maintainability**: Each model is self-contained and easy to modify
2. **Readability**: Smaller files are easier to understand and navigate
3. **Reusability**: Models can be imported individually if needed
4. **Testing**: Each model can be tested in isolation
5. **Collaboration**: Multiple developers can work on different models simultaneously
6. **IDE Support**: Better file navigation and search capabilities

## Migration from Monolithic File

The original `firebase-models.js` file has been split into these individual files. All functionality remains the same, but the code is now better organized and more maintainable.

## Future Enhancements

Potential improvements for the model system:

1. **Validation**: Add comprehensive data validation to each model
2. **Serialization**: Add custom serialization methods for different formats
3. **Caching**: Implement intelligent caching mechanisms
4. **Events**: Add event system for model changes
5. **Relationships**: Add support for model relationships and joins
6. **Migrations**: Add data migration utilities for schema changes
