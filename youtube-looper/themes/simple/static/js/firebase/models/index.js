// ============================================================================
// FIREBASE MODELS INDEX
// ============================================================================

/**
 * This file serves as the main entry point for all Firebase model classes.
 * It ensures models are loaded in the correct dependency order.
 * 
 * Loading Order:
 * 1. FirebaseModel (base class)
 * 2. VideoModel (used by QueueDataModel)
 * 3. QueueDataModel (used by QueueMetadataModel and queue models)
 * 4. QueueMetadataModel (used by PublicQueueModel)
 * 5. PublicQueueModel and PersonalQueueModel (collection models)
 * 
 * All model files are loaded via script tags in the HTML file.
 * This index file just provides documentation and validation.
 */

/**
 * Validate that all required model classes are loaded
 * @returns {Object} Validation result
 */
function validateModelsLoaded() {
  const requiredClasses = [
    'FirebaseModel',
    'VideoModel', 
    'QueueDataModel',
    'QueueMetadataModel',
    'PublicQueueModel',
    'PersonalQueueModel'
  ];

  const missing = [];
  const loaded = [];

  requiredClasses.forEach(className => {
    if (typeof window[className] !== 'undefined') {
      loaded.push(className);
    } else {
      missing.push(className);
    }
  });

  return {
    allLoaded: missing.length === 0,
    loaded,
    missing,
    total: requiredClasses.length
  };
}

/**
 * Get model class information
 * @returns {Object} Model class information
 */
function getModelInfo() {
  const validation = validateModelsLoaded();
  
  if (!validation.allLoaded) {
    console.warn('⚠️ Some model classes are missing:', validation.missing);
    return validation;
  }

  return {
    ...validation,
    models: {
      FirebaseModel: {
        description: 'Base model class with common functionality',
        methods: ['get', 'set', 'toObject', 'isDirty', 'reset', 'markSaved']
      },
      VideoModel: {
        description: 'Individual video model with metadata',
        fields: VideoModel.FIELDS,
        methods: ['getFormattedDuration', 'isValid', 'getVideoUrl', 'getThumbnailUrl']
      },
      QueueDataModel: {
        description: 'Queue content and playback state model',
        fields: QueueDataModel.FIELDS,
        methods: ['getVideoCount', 'getTotalDuration', 'getFirstVideoThumbnail', 'getCurrentVideo', 'addVideo', 'removeVideo']
      },
      QueueMetadataModel: {
        description: 'Queue metadata for efficient querying',
        fields: QueueMetadataModel.FIELDS,
        methods: ['getFormattedTotalDuration', 'getRelativeLastModified', 'isPopular', 'isRecent', 'isLong']
      },
      PublicQueueModel: {
        description: 'Public shared queue model',
        collection: PublicQueueModel.COLLECTION,
        fields: PublicQueueModel.FIELDS,
        methods: ['getTitle', 'getVideoCount', 'getTotalDuration', 'getViewCount', 'getPreviewData']
      },
      PersonalQueueModel: {
        description: 'Personal user queue model',
        collection: PersonalQueueModel.COLLECTION,
        fields: PersonalQueueModel.FIELDS,
        methods: ['getTitle', 'getVideoCount', 'belongsToUser', 'togglePublic', 'getDisplayData']
      }
    }
  };
}

/**
 * Test model functionality
 * @returns {Object} Test results
 */
function testModels() {
  const validation = validateModelsLoaded();
  
  if (!validation.allLoaded) {
    return {
      success: false,
      error: 'Not all models are loaded',
      missing: validation.missing
    };
  }

  try {
    // Test VideoModel
    const video = new VideoModel({
      [VideoModel.FIELDS.ID]: 'test123',
      [VideoModel.FIELDS.TITLE]: 'Test Video',
      [VideoModel.FIELDS.DURATION]: 180
    });
    
    if (video.id !== 'test123' || video.title !== 'Test Video' || video.duration !== 180) {
      throw new Error('VideoModel test failed');
    }

    // Test QueueDataModel
    const queueData = new QueueDataModel({
      [QueueDataModel.FIELDS.QUEUE]: [video.toObject()],
      [QueueDataModel.FIELDS.TITLE]: 'Test Queue'
    });
    
    if (queueData.getVideoCount() !== 1 || queueData.title !== 'Test Queue') {
      throw new Error('QueueDataModel test failed');
    }

    // Test QueueMetadataModel
    const metadata = QueueMetadataModel.fromQueueData(queueData);
    
    if (metadata.videoCount !== 1 || metadata.totalDuration !== 180) {
      throw new Error('QueueMetadataModel test failed');
    }

    // Test PublicQueueModel
    const publicQueue = new PublicQueueModel({
      [PublicQueueModel.FIELDS.ID]: 'queue123',
      [PublicQueueModel.FIELDS.QUEUE_DATA]: queueData.toObject(),
      [PublicQueueModel.FIELDS.METADATA]: metadata.toObject()
    });
    
    if (publicQueue.id !== 'queue123' || publicQueue.getVideoCount() !== 1) {
      throw new Error('PublicQueueModel test failed');
    }

    // Test PersonalQueueModel
    const personalQueue = new PersonalQueueModel({
      [PersonalQueueModel.FIELDS.ID]: 'personal123',
      [PersonalQueueModel.FIELDS.USER_ID]: 'user123',
      [PersonalQueueModel.FIELDS.QUEUE_DATA]: queueData.toObject()
    });
    
    if (personalQueue.id !== 'personal123' || personalQueue.userId !== 'user123') {
      throw new Error('PersonalQueueModel test failed');
    }

    return {
      success: true,
      message: 'All model tests passed',
      tested: validation.loaded
    };

  } catch (error) {
    return {
      success: false,
      error: error.message,
      tested: validation.loaded
    };
  }
}

// Export functions to global scope for console testing
window.validateModelsLoaded = validateModelsLoaded;
window.getModelInfo = getModelInfo;
window.testModels = testModels;

// Auto-validate when this file loads
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    const validation = validateModelsLoaded();
    
    if (validation.allLoaded) {
      console.log('✅ All Firebase models loaded successfully:', validation.loaded);
      
      // Run tests
      const testResults = testModels();
      if (testResults.success) {
        console.log('✅ All model tests passed');
      } else {
        console.error('❌ Model tests failed:', testResults.error);
      }
    } else {
      console.error('❌ Missing Firebase models:', validation.missing);
    }
  }, 100); // Small delay to ensure all scripts are loaded
});

console.log('✅ Firebase Models Index loaded');
console.log('💡 Available functions: validateModelsLoaded(), getModelInfo(), testModels()');
