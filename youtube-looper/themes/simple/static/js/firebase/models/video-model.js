// ============================================================================
// VIDEO MODEL CLASS
// ============================================================================

/**
 * Video model for individual videos in queues
 * Represents a single video with all its metadata
 */
class VideoModel extends FirebaseModel {
  static FIELDS = {
    ID: 'id',
    TITLE: 'title',
    THUMBNAIL: 'thumbnail',
    DURATION: 'duration',
    CHANNEL: 'channel',
    DESCRIPTION: 'description',
    PUBLISHED_AT: 'publishedAt',
    VIEW_COUNT: 'viewCount'
  };

  constructor(data = {}) {
    super(data);
  }

  // Getters
  get id() { return this.get(VideoModel.FIELDS.ID); }
  get title() { return this.get(VideoModel.FIELDS.TITLE); }
  get thumbnail() { return this.get(VideoModel.FIELDS.THUMBNAIL); }
  get duration() { return this.get(VideoModel.FIELDS.DURATION); }
  get channel() { return this.get(VideoModel.FIELDS.CHANNEL); }
  get description() { return this.get(VideoModel.FIELDS.DESCRIPTION); }
  get publishedAt() { return this.get(VideoModel.FIELDS.PUBLISHED_AT); }
  get viewCount() { return this.get(VideoModel.FIELDS.VIEW_COUNT); }

  // Setters
  set id(value) { this.set(VideoModel.FIELDS.ID, value); }
  set title(value) { this.set(VideoModel.FIELDS.TITLE, value); }
  set thumbnail(value) { this.set(VideoModel.FIELDS.THUMBNAIL, value); }
  set duration(value) { this.set(VideoModel.FIELDS.DURATION, value); }
  set channel(value) { this.set(VideoModel.FIELDS.CHANNEL, value); }
  set description(value) { this.set(VideoModel.FIELDS.DESCRIPTION, value); }
  set publishedAt(value) { this.set(VideoModel.FIELDS.PUBLISHED_AT, value); }
  set viewCount(value) { this.set(VideoModel.FIELDS.VIEW_COUNT, value); }

  /**
   * Get formatted duration string
   * @returns {string} Formatted duration (e.g., "3:45")
   */
  getFormattedDuration() {
    if (!this.duration) return '0:00';
    
    const minutes = Math.floor(this.duration / 60);
    const seconds = this.duration % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  /**
   * Check if video has valid data
   * @returns {boolean} True if video has required fields
   */
  isValid() {
    return !!(this.id && this.title);
  }

  /**
   * Get video URL
   * @returns {string} YouTube video URL
   */
  getVideoUrl() {
    return this.id ? `https://www.youtube.com/watch?v=${this.id}` : '';
  }

  /**
   * Get thumbnail URL with specific quality
   * @param {string} quality - Thumbnail quality ('default', 'medium', 'high', 'standard', 'maxres')
   * @returns {string} Thumbnail URL
   */
  getThumbnailUrl(quality = 'medium') {
    if (!this.id) return '';
    
    const qualityMap = {
      'default': 'default',
      'medium': 'mqdefault',
      'high': 'hqdefault',
      'standard': 'sddefault',
      'maxres': 'maxresdefault'
    };
    
    const qualityParam = qualityMap[quality] || 'mqdefault';
    return `https://img.youtube.com/vi/${this.id}/${qualityParam}.jpg`;
  }

  /**
   * Create VideoModel from plain object
   * @param {Object} data - Video data
   * @returns {VideoModel} Video model instance
   */
  static fromObject(data) {
    return new VideoModel(data);
  }

  /**
   * Create VideoModel array from plain objects
   * @param {Array} dataArray - Array of video data
   * @returns {Array<VideoModel>} Array of video models
   */
  static fromArray(dataArray) {
    if (!Array.isArray(dataArray)) return [];
    return dataArray.map(data => VideoModel.fromObject(data));
  }

  /**
   * Create VideoModel from YouTube API response
   * @param {Object} apiData - YouTube API video data
   * @returns {VideoModel} Video model instance
   */
  static fromYouTubeAPI(apiData) {
    const snippet = apiData.snippet || {};
    const statistics = apiData.statistics || {};
    const contentDetails = apiData.contentDetails || {};
    
    // Parse ISO 8601 duration (PT4M13S) to seconds
    let duration = 0;
    if (contentDetails.duration) {
      const match = contentDetails.duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
      if (match) {
        const hours = parseInt(match[1]) || 0;
        const minutes = parseInt(match[2]) || 0;
        const seconds = parseInt(match[3]) || 0;
        duration = hours * 3600 + minutes * 60 + seconds;
      }
    }

    return new VideoModel({
      [VideoModel.FIELDS.ID]: apiData.id,
      [VideoModel.FIELDS.TITLE]: snippet.title,
      [VideoModel.FIELDS.THUMBNAIL]: snippet.thumbnails?.medium?.url || snippet.thumbnails?.default?.url,
      [VideoModel.FIELDS.DURATION]: duration,
      [VideoModel.FIELDS.CHANNEL]: snippet.channelTitle,
      [VideoModel.FIELDS.DESCRIPTION]: snippet.description,
      [VideoModel.FIELDS.PUBLISHED_AT]: snippet.publishedAt,
      [VideoModel.FIELDS.VIEW_COUNT]: parseInt(statistics.viewCount) || 0
    });
  }

  /**
   * Validate video data
   * @param {Object} data - Video data to validate
   * @returns {Object} Validation result with isValid and errors
   */
  static validate(data) {
    const errors = [];
    
    if (!data[VideoModel.FIELDS.ID]) {
      errors.push('Video ID is required');
    }
    
    if (!data[VideoModel.FIELDS.TITLE]) {
      errors.push('Video title is required');
    }
    
    if (data[VideoModel.FIELDS.DURATION] && (typeof data[VideoModel.FIELDS.DURATION] !== 'number' || data[VideoModel.FIELDS.DURATION] < 0)) {
      errors.push('Duration must be a positive number');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

console.log('✅ Video Model loaded');
