// ============================================================================
// PUBLIC QUEUE MODEL CLASS
// ============================================================================

/**
 * Public shared queue model (queues collection)
 * Represents public queues that can be shared and viewed by anyone
 */
class PublicQueueModel extends FirebaseModel {
  static COLLECTION = 'queues';

  static FIELDS = {
    ID: 'id',
    QUEUE_DATA: 'queueData',
    METADATA: 'metadata'
  };

  constructor(data = {}) {
    super(data);
  }

  // Getters
  get id() { return this.get(PublicQueueModel.FIELDS.ID); }
  get queueData() {
    const data = this.get(PublicQueueModel.FIELDS.QUEUE_DATA);
    return data ? QueueDataModel.fromObject(data) : new QueueDataModel();
  }
  get metadata() {
    const data = this.get(PublicQueueModel.FIELDS.METADATA);
    return data ? QueueMetadataModel.fromObject(data) : new QueueMetadataModel();
  }

  // Setters
  set id(value) { this.set(PublicQueueModel.FIELDS.ID, value); }
  set queueData(value) {
    const data = value instanceof QueueDataModel ? value.toObject() : value;
    this.set(PublicQueueModel.FIELDS.QUEUE_DATA, data);
  }
  set metadata(value) {
    const data = value instanceof QueueMetadataModel ? value.toObject() : value;
    this.set(PublicQueueModel.FIELDS.METADATA, data);
  }

  /**
   * Get queue title from metadata
   * @returns {string} Queue title
   */
  getTitle() {
    return this.metadata.title || this.queueData.title || 'Untitled Queue';
  }

  /**
   * Get video count from metadata
   * @returns {number} Number of videos
   */
  getVideoCount() {
    return this.metadata.videoCount || this.queueData.getVideoCount();
  }

  /**
   * Get total duration from metadata
   * @returns {number} Total duration in seconds
   */
  getTotalDuration() {
    return this.metadata.totalDuration || this.queueData.getTotalDuration();
  }

  /**
   * Get view count from metadata
   * @returns {number} View count
   */
  getViewCount() {
    return this.metadata.viewCount || 0;
  }

  /**
   * Get first video thumbnail from metadata
   * @returns {string} Thumbnail URL
   */
  getThumbnail() {
    return this.metadata.firstVideoThumbnail || this.queueData.getFirstVideoThumbnail();
  }

  /**
   * Get creation date from metadata
   * @returns {Date|null} Creation date
   */
  getCreatedAt() {
    const createdAt = this.metadata.createdAt;
    if (!createdAt) return null;
    return createdAt.toDate ? createdAt.toDate() : new Date(createdAt);
  }

  /**
   * Get last modified date from metadata
   * @returns {Date|null} Last modified date
   */
  getLastModified() {
    const lastModified = this.metadata.lastModified;
    if (!lastModified) return null;
    return lastModified.toDate ? lastModified.toDate() : new Date(lastModified);
  }

  /**
   * Check if queue is empty
   * @returns {boolean} True if queue has no videos
   */
  isEmpty() {
    return this.getVideoCount() === 0;
  }

  /**
   * Check if queue is popular
   * @returns {boolean} True if queue has many views
   */
  isPopular() {
    return this.metadata.isPopular();
  }

  /**
   * Check if queue is recent
   * @returns {boolean} True if queue was created recently
   */
  isRecent() {
    return this.metadata.isRecent();
  }

  /**
   * Update metadata from queue data
   */
  updateMetadataFromQueueData() {
    const newMetadata = QueueMetadataModel.fromQueueData(this.queueData);
    
    // Preserve existing metadata fields
    newMetadata.createdAt = this.metadata.createdAt;
    newMetadata.viewCount = this.metadata.viewCount;
    
    // Update last modified
    newMetadata.updateLastModified();
    
    this.metadata = newMetadata;
  }

  /**
   * Increment view count
   */
  incrementViewCount() {
    this.metadata.incrementViewCount();
  }

  /**
   * Get shareable URL
   * @param {string} baseUrl - Base URL of the application
   * @returns {string} Shareable URL
   */
  getShareableUrl(baseUrl = window.location.origin) {
    return `${baseUrl}?queue=${this.id}`;
  }

  /**
   * Get preview data for display
   * @returns {Object} Preview data
   */
  getPreviewData() {
    const videos = this.queueData.queue.slice(0, 3);
    return {
      id: this.id,
      title: this.getTitle(),
      videoCount: this.getVideoCount(),
      totalDuration: this.getTotalDuration(),
      formattedDuration: this.metadata.getFormattedTotalDuration(),
      viewCount: this.getViewCount(),
      thumbnail: this.getThumbnail(),
      createdAt: this.getCreatedAt(),
      lastModified: this.getLastModified(),
      relativeTime: this.metadata.getRelativeLastModified(),
      preview: videos.map(video => ({
        title: video.title,
        thumbnail: video.thumbnail,
        duration: video.getFormattedDuration()
      })),
      category: this.metadata.getCategory()
    };
  }

  /**
   * Create PublicQueueModel from plain object
   * @param {Object} data - Queue document data
   * @returns {PublicQueueModel} Public queue model instance
   */
  static fromObject(data) {
    return new PublicQueueModel(data);
  }

  /**
   * Create PublicQueueModel from Firebase document
   * @param {Object} doc - Firebase document snapshot
   * @returns {PublicQueueModel} Public queue model instance
   */
  static fromFirebaseDoc(doc) {
    const data = doc.data();
    data[PublicQueueModel.FIELDS.ID] = doc.id;
    return PublicQueueModel.fromObject(data);
  }

  /**
   * Create PublicQueueModel from current application state
   * @param {string} title - Queue title
   * @param {string} queueId - Optional queue ID
   * @returns {PublicQueueModel} Public queue model instance
   */
  static fromCurrentState(title = 'Shared Queue', queueId = null) {
    const queueData = QueueDataModel.fromCurrentState(title);
    const metadata = QueueMetadataModel.fromQueueData(queueData);
    
    return new PublicQueueModel({
      [PublicQueueModel.FIELDS.ID]: queueId,
      [PublicQueueModel.FIELDS.QUEUE_DATA]: queueData.toObject(),
      [PublicQueueModel.FIELDS.METADATA]: metadata.toObject()
    });
  }

  /**
   * Prepare data for Firebase save
   * @returns {Object} Data ready for Firebase
   */
  toFirebaseData() {
    const data = this.toObject();
    // Remove id from data as it's used as document ID
    delete data[PublicQueueModel.FIELDS.ID];
    return data;
  }

  /**
   * Validate public queue data
   * @param {Object} data - Queue data to validate
   * @returns {Object} Validation result with isValid and errors
   */
  static validate(data) {
    const errors = [];
    
    // Validate queue data
    const queueData = data[PublicQueueModel.FIELDS.QUEUE_DATA];
    if (!queueData) {
      errors.push('Queue data is required');
    } else {
      const queueValidation = QueueDataModel.validate(queueData);
      if (!queueValidation.isValid) {
        errors.push(...queueValidation.errors.map(err => `Queue data: ${err}`));
      }
    }
    
    // Validate metadata
    const metadata = data[PublicQueueModel.FIELDS.METADATA];
    if (!metadata) {
      errors.push('Metadata is required');
    } else {
      const metadataValidation = QueueMetadataModel.validate(metadata);
      if (!metadataValidation.isValid) {
        errors.push(...metadataValidation.errors.map(err => `Metadata: ${err}`));
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

console.log('✅ Public Queue Model loaded');
