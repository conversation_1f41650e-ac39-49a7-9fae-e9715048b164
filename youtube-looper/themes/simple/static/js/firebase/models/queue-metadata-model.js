// ============================================================================
// QUEUE METADATA MODEL CLASS
// ============================================================================

/**
 * Queue metadata model
 * Represents metadata for efficient querying and display of queues
 */
class QueueMetadataModel extends FirebaseModel {
  static FIELDS = {
    TITLE: 'title',
    VIDEO_COUNT: 'videoCount',
    TOTAL_DURATION: 'totalDuration',
    FIRST_VIDEO_THUMBNAIL: 'firstVideoThumbnail',
    CREATED_AT: 'createdAt',
    LAST_MODIFIED: 'lastModified',
    VIEW_COUNT: 'viewCount'
  };

  constructor(data = {}) {
    super(data);
  }

  // Getters
  get title() { return this.get(QueueMetadataModel.FIELDS.TITLE); }
  get videoCount() { return this.get(QueueMetadataModel.FIELDS.VIDEO_COUNT) || 0; }
  get totalDuration() { return this.get(QueueMetadataModel.FIELDS.TOTAL_DURATION) || 0; }
  get firstVideoThumbnail() { return this.get(QueueMetadataModel.FIELDS.FIRST_VIDEO_THUMBNAIL); }
  get createdAt() { return this.get(QueueMetadataModel.FIELDS.CREATED_AT); }
  get lastModified() { return this.get(QueueMetadataModel.FIELDS.LAST_MODIFIED); }
  get viewCount() { return this.get(QueueMetadataModel.FIELDS.VIEW_COUNT) || 0; }

  // Setters
  set title(value) { this.set(QueueMetadataModel.FIELDS.TITLE, value); }
  set videoCount(value) { this.set(QueueMetadataModel.FIELDS.VIDEO_COUNT, value); }
  set totalDuration(value) { this.set(QueueMetadataModel.FIELDS.TOTAL_DURATION, value); }
  set firstVideoThumbnail(value) { this.set(QueueMetadataModel.FIELDS.FIRST_VIDEO_THUMBNAIL, value); }
  set createdAt(value) { this.set(QueueMetadataModel.FIELDS.CREATED_AT, value); }
  set lastModified(value) { this.set(QueueMetadataModel.FIELDS.LAST_MODIFIED, value); }
  set viewCount(value) { this.set(QueueMetadataModel.FIELDS.VIEW_COUNT, value); }

  /**
   * Get formatted total duration
   * @returns {string} Formatted duration (e.g., "1:23:45")
   */
  getFormattedTotalDuration() {
    const totalSeconds = this.totalDuration;
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
  }

  /**
   * Get formatted creation date
   * @returns {string} Formatted date
   */
  getFormattedCreatedAt() {
    if (!this.createdAt) return 'Unknown';
    
    const date = this.createdAt.toDate ? this.createdAt.toDate() : new Date(this.createdAt);
    return date.toLocaleDateString();
  }

  /**
   * Get formatted last modified date
   * @returns {string} Formatted date
   */
  getFormattedLastModified() {
    if (!this.lastModified) return 'Unknown';
    
    const date = this.lastModified.toDate ? this.lastModified.toDate() : new Date(this.lastModified);
    return date.toLocaleDateString();
  }

  /**
   * Get relative time since last modified
   * @returns {string} Relative time (e.g., "2 hours ago")
   */
  getRelativeLastModified() {
    if (!this.lastModified) return 'Unknown';
    
    const date = this.lastModified.toDate ? this.lastModified.toDate() : new Date(this.lastModified);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minute${diffMins === 1 ? '' : 's'} ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours === 1 ? '' : 's'} ago`;
    if (diffDays < 7) return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`;

    return date.toLocaleDateString();
  }

  /**
   * Check if queue is popular (has many views)
   * @returns {boolean} True if queue is considered popular
   */
  isPopular() {
    return this.viewCount >= 100; // Configurable threshold
  }

  /**
   * Check if queue is recent (created within last week)
   * @returns {boolean} True if queue is recent
   */
  isRecent() {
    if (!this.createdAt) return false;
    
    const date = this.createdAt.toDate ? this.createdAt.toDate() : new Date(this.createdAt);
    const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    return date > weekAgo;
  }

  /**
   * Check if queue is long (has many videos or long duration)
   * @returns {boolean} True if queue is considered long
   */
  isLong() {
    return this.videoCount >= 20 || this.totalDuration >= 3600; // 20+ videos or 1+ hour
  }

  /**
   * Get queue category based on metadata
   * @returns {string} Queue category
   */
  getCategory() {
    if (this.isPopular()) return 'popular';
    if (this.isRecent()) return 'recent';
    if (this.isLong()) return 'long';
    return 'standard';
  }

  /**
   * Increment view count
   */
  incrementViewCount() {
    this.viewCount = this.viewCount + 1;
  }

  /**
   * Update last modified timestamp
   */
  updateLastModified() {
    this.lastModified = new Date();
  }

  /**
   * Create metadata from queue data
   * @param {QueueDataModel} queueData - Queue data model
   * @returns {QueueMetadataModel} Metadata model
   */
  static fromQueueData(queueData) {
    return new QueueMetadataModel({
      [QueueMetadataModel.FIELDS.TITLE]: queueData.title,
      [QueueMetadataModel.FIELDS.VIDEO_COUNT]: queueData.getVideoCount(),
      [QueueMetadataModel.FIELDS.TOTAL_DURATION]: queueData.getTotalDuration(),
      [QueueMetadataModel.FIELDS.FIRST_VIDEO_THUMBNAIL]: queueData.getFirstVideoThumbnail(),
      [QueueMetadataModel.FIELDS.VIEW_COUNT]: 0
    });
  }

  /**
   * Create QueueMetadataModel from plain object
   * @param {Object} data - Metadata
   * @returns {QueueMetadataModel} Metadata model instance
   */
  static fromObject(data) {
    return new QueueMetadataModel(data);
  }

  /**
   * Create metadata from legacy queue data
   * @param {Object} legacyData - Legacy queue data
   * @returns {QueueMetadataModel} Metadata model
   */
  static fromLegacyData(legacyData) {
    const queue = legacyData.queue || [];
    const totalDuration = queue.reduce((total, video) => total + (video.duration || 0), 0);
    const firstVideoThumbnail = queue.length > 0 ? queue[0].thumbnail : '';

    return new QueueMetadataModel({
      [QueueMetadataModel.FIELDS.TITLE]: legacyData.title || 'Untitled Queue',
      [QueueMetadataModel.FIELDS.VIDEO_COUNT]: queue.length,
      [QueueMetadataModel.FIELDS.TOTAL_DURATION]: totalDuration,
      [QueueMetadataModel.FIELDS.FIRST_VIDEO_THUMBNAIL]: firstVideoThumbnail,
      [QueueMetadataModel.FIELDS.CREATED_AT]: legacyData.timestamp ? new Date(legacyData.timestamp) : new Date(),
      [QueueMetadataModel.FIELDS.LAST_MODIFIED]: new Date(),
      [QueueMetadataModel.FIELDS.VIEW_COUNT]: 0
    });
  }

  /**
   * Validate metadata
   * @param {Object} data - Metadata to validate
   * @returns {Object} Validation result with isValid and errors
   */
  static validate(data) {
    const errors = [];
    
    if (!data[QueueMetadataModel.FIELDS.TITLE]) {
      errors.push('Title is required');
    }
    
    const videoCount = data[QueueMetadataModel.FIELDS.VIDEO_COUNT];
    if (videoCount !== undefined && (typeof videoCount !== 'number' || videoCount < 0)) {
      errors.push('Video count must be a non-negative number');
    }
    
    const totalDuration = data[QueueMetadataModel.FIELDS.TOTAL_DURATION];
    if (totalDuration !== undefined && (typeof totalDuration !== 'number' || totalDuration < 0)) {
      errors.push('Total duration must be a non-negative number');
    }
    
    const viewCount = data[QueueMetadataModel.FIELDS.VIEW_COUNT];
    if (viewCount !== undefined && (typeof viewCount !== 'number' || viewCount < 0)) {
      errors.push('View count must be a non-negative number');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

console.log('✅ Queue Metadata Model loaded');
