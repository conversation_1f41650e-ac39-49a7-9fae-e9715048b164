// ============================================================================
// QUEUE DATA MODEL CLASS
// ============================================================================

/**
 * Queue data model for the actual queue content
 * Represents the queue content, playback state, and metadata
 */
class QueueDataModel extends FirebaseModel {
  static FIELDS = {
    QUEUE: 'queue',
    CURRENT_INDEX: 'currentIndex',
    IS_PLAYING: 'isPlaying',
    TIMESTAMP: 'timestamp',
    TITLE: 'title'
  };

  constructor(data = {}) {
    super(data);
  }

  // Getters
  get queue() { 
    const queueData = this.get(QueueDataModel.FIELDS.QUEUE) || [];
    return VideoModel.fromArray(queueData);
  }
  get currentIndex() { return this.get(QueueDataModel.FIELDS.CURRENT_INDEX) || 0; }
  get isPlaying() { return this.get(QueueDataModel.FIELDS.IS_PLAYING) || false; }
  get timestamp() { return this.get(QueueDataModel.FIELDS.TIMESTAMP); }
  get title() { return this.get(QueueDataModel.FIELDS.TITLE); }

  // Setters
  set queue(videos) { 
    const videoData = videos.map(video => 
      video instanceof VideoModel ? video.toObject() : video
    );
    this.set(QueueDataModel.FIELDS.QUEUE, videoData);
  }
  set currentIndex(value) { this.set(QueueDataModel.FIELDS.CURRENT_INDEX, value); }
  set isPlaying(value) { this.set(QueueDataModel.FIELDS.IS_PLAYING, value); }
  set timestamp(value) { this.set(QueueDataModel.FIELDS.TIMESTAMP, value); }
  set title(value) { this.set(QueueDataModel.FIELDS.TITLE, value); }

  /**
   * Get video count
   * @returns {number} Number of videos in queue
   */
  getVideoCount() {
    return this.queue.length;
  }

  /**
   * Get total duration in seconds
   * @returns {number} Total duration
   */
  getTotalDuration() {
    return this.queue.reduce((total, video) => total + (video.duration || 0), 0);
  }

  /**
   * Get formatted total duration
   * @returns {string} Formatted duration (e.g., "1:23:45")
   */
  getFormattedTotalDuration() {
    const totalSeconds = this.getTotalDuration();
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
  }

  /**
   * Get first video thumbnail
   * @returns {string} Thumbnail URL
   */
  getFirstVideoThumbnail() {
    return this.queue.length > 0 ? this.queue[0].thumbnail : '';
  }

  /**
   * Get current video
   * @returns {VideoModel|null} Current video or null
   */
  getCurrentVideo() {
    if (this.currentIndex >= 0 && this.currentIndex < this.queue.length) {
      return this.queue[this.currentIndex];
    }
    return null;
  }

  /**
   * Add video to queue
   * @param {VideoModel|Object} video - Video to add
   * @returns {boolean} True if added successfully
   */
  addVideo(video) {
    const videoModel = video instanceof VideoModel ? video : VideoModel.fromObject(video);
    
    // Check if video already exists
    const exists = this.queue.some(v => v.id === videoModel.id);
    if (exists) {
      return false;
    }

    const currentQueue = this.queue;
    currentQueue.push(videoModel);
    this.queue = currentQueue;
    return true;
  }

  /**
   * Remove video from queue
   * @param {number} index - Index of video to remove
   * @returns {boolean} True if removed successfully
   */
  removeVideo(index) {
    if (index < 0 || index >= this.queue.length) {
      return false;
    }

    const currentQueue = this.queue;
    currentQueue.splice(index, 1);
    this.queue = currentQueue;

    // Adjust current index if necessary
    if (this.currentIndex >= index && this.currentIndex > 0) {
      this.currentIndex = this.currentIndex - 1;
    } else if (this.currentIndex >= this.queue.length) {
      this.currentIndex = Math.max(0, this.queue.length - 1);
    }

    return true;
  }

  /**
   * Move video to new position
   * @param {number} fromIndex - Current index
   * @param {number} toIndex - New index
   * @returns {boolean} True if moved successfully
   */
  moveVideo(fromIndex, toIndex) {
    if (fromIndex < 0 || fromIndex >= this.queue.length || 
        toIndex < 0 || toIndex >= this.queue.length) {
      return false;
    }

    const currentQueue = this.queue;
    const video = currentQueue.splice(fromIndex, 1)[0];
    currentQueue.splice(toIndex, 0, video);
    this.queue = currentQueue;

    // Adjust current index if necessary
    if (this.currentIndex === fromIndex) {
      this.currentIndex = toIndex;
    } else if (fromIndex < this.currentIndex && toIndex >= this.currentIndex) {
      this.currentIndex = this.currentIndex - 1;
    } else if (fromIndex > this.currentIndex && toIndex <= this.currentIndex) {
      this.currentIndex = this.currentIndex + 1;
    }

    return true;
  }

  /**
   * Clear all videos from queue
   */
  clearQueue() {
    this.queue = [];
    this.currentIndex = 0;
    this.isPlaying = false;
  }

  /**
   * Check if queue is empty
   * @returns {boolean} True if queue is empty
   */
  isEmpty() {
    return this.queue.length === 0;
  }

  /**
   * Check if queue has next video
   * @returns {boolean} True if there's a next video
   */
  hasNext() {
    return this.currentIndex < this.queue.length - 1;
  }

  /**
   * Check if queue has previous video
   * @returns {boolean} True if there's a previous video
   */
  hasPrevious() {
    return this.currentIndex > 0;
  }

  /**
   * Get next video index
   * @returns {number} Next video index or current if at end
   */
  getNextIndex() {
    return this.hasNext() ? this.currentIndex + 1 : this.currentIndex;
  }

  /**
   * Get previous video index
   * @returns {number} Previous video index or current if at start
   */
  getPreviousIndex() {
    return this.hasPrevious() ? this.currentIndex - 1 : this.currentIndex;
  }

  /**
   * Create QueueDataModel from plain object
   * @param {Object} data - Queue data
   * @returns {QueueDataModel} Queue data model instance
   */
  static fromObject(data) {
    return new QueueDataModel(data);
  }

  /**
   * Create QueueDataModel from current application state
   * @param {string} title - Queue title
   * @returns {QueueDataModel} Queue data model instance
   */
  static fromCurrentState(title = 'Untitled Queue') {
    return new QueueDataModel({
      [QueueDataModel.FIELDS.QUEUE]: getVideoQueue ? getVideoQueue() : [],
      [QueueDataModel.FIELDS.CURRENT_INDEX]: getCurrentVideoIndex ? getCurrentVideoIndex() : 0,
      [QueueDataModel.FIELDS.IS_PLAYING]: getIsPlaying ? getIsPlaying() : false,
      [QueueDataModel.FIELDS.TIMESTAMP]: Date.now(),
      [QueueDataModel.FIELDS.TITLE]: title
    });
  }

  /**
   * Validate queue data
   * @param {Object} data - Queue data to validate
   * @returns {Object} Validation result with isValid and errors
   */
  static validate(data) {
    const errors = [];
    
    if (!data[QueueDataModel.FIELDS.TITLE]) {
      errors.push('Queue title is required');
    }
    
    if (!Array.isArray(data[QueueDataModel.FIELDS.QUEUE])) {
      errors.push('Queue must be an array');
    }
    
    const currentIndex = data[QueueDataModel.FIELDS.CURRENT_INDEX];
    if (currentIndex !== undefined && (typeof currentIndex !== 'number' || currentIndex < 0)) {
      errors.push('Current index must be a non-negative number');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

console.log('✅ Queue Data Model loaded');
