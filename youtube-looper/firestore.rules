rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read/write access to all documents for authenticated users
    // This is a permissive rule for development - consider tightening for production
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
    
    // Personal queues - users can only access their own queues
    match /personal_queues/{queueId} {
      allow read, write: if request.auth != null && 
        (resource == null || resource.data.userId == request.auth.uid);
    }
    
    // Public queues - readable by all authenticated users (including anonymous)
    match /queues/{queueId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.token.firebase.sign_in_provider != 'anonymous' &&
        (resource == null || resource.data.createdBy == request.auth.uid);
    }
  }
}
