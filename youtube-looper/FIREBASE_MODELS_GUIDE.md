# Firebase Models Guide

This guide explains how to use the new Firebase model system to avoid using string keys directly and provide better type safety and structure.

## Overview

The new model system consists of three layers:

1. **Models** (`firebase-models.js`) - Define data structure and field constants
2. **Repositories** (`firebase-repositories.js`) - Handle Firebase operations
3. **Services** (`firebase-services.js`) - Provide high-level business logic

## Models

### VideoModel

Represents individual videos in queues.

```javascript
// Create a video
const video = new VideoModel({
  [VideoModel.FIELDS.ID]: 'dQw4w9WgXcQ',
  [VideoModel.FIELDS.TITLE]: '<PERSON> - Never Gonna Give You Up',
  [VideoModel.FIELDS.THUMBNAIL]: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
  [VideoModel.FIELDS.DURATION]: 212,
  [VideoModel.FIELDS.CHANNEL]: '<PERSON>'
});

// Access properties
console.log(video.title); // "<PERSON> - Never Gonna Give You Up"
console.log(video.duration); // 212

// Modify properties
video.title = "New Title";
video.duration = 300;

// Convert to plain object for Firebase
const videoData = video.toObject();
```

### QueueDataModel

Represents the actual queue content and playback state.

```javascript
// Create queue data
const queueData = new QueueDataModel({
  [QueueDataModel.FIELDS.QUEUE]: [video1.toObject(), video2.toObject()],
  [QueueDataModel.FIELDS.CURRENT_INDEX]: 0,
  [QueueDataModel.FIELDS.IS_PLAYING]: false,
  [QueueDataModel.FIELDS.TIMESTAMP]: Date.now(),
  [QueueDataModel.FIELDS.TITLE]: 'My Playlist'
});

// Access properties
console.log(queueData.queue); // Array of VideoModel instances
console.log(queueData.currentIndex); // 0
console.log(queueData.getVideoCount()); // 2
console.log(queueData.getTotalDuration()); // Sum of all video durations
```

### QueueMetadataModel

Represents queue metadata for efficient querying and display.

```javascript
// Create metadata from queue data
const metadata = QueueMetadataModel.fromQueueData(queueData);

// Or create manually
const metadata = new QueueMetadataModel({
  [QueueMetadataModel.FIELDS.TITLE]: 'My Playlist',
  [QueueMetadataModel.FIELDS.VIDEO_COUNT]: 5,
  [QueueMetadataModel.FIELDS.TOTAL_DURATION]: 1200,
  [QueueMetadataModel.FIELDS.VIEW_COUNT]: 0
});
```

### PublicQueueModel

Represents public shared queues in the `queues` collection.

```javascript
// Create a public queue
const publicQueue = new PublicQueueModel({
  [PublicQueueModel.FIELDS.ID]: 'queue123',
  [PublicQueueModel.FIELDS.QUEUE_DATA]: queueData.toObject(),
  [PublicQueueModel.FIELDS.METADATA]: metadata.toObject()
});

// Access nested data
console.log(publicQueue.queueData.title);
console.log(publicQueue.metadata.videoCount);
```

### PersonalQueueModel

Represents personal queues in the `personal_queues` collection.

```javascript
// Create a personal queue
const personalQueue = new PersonalQueueModel({
  [PersonalQueueModel.FIELDS.ID]: 'personal_user123_1234567890',
  [PersonalQueueModel.FIELDS.USER_ID]: 'user123',
  [PersonalQueueModel.FIELDS.QUEUE_DATA]: queueData.toObject(),
  [PersonalQueueModel.FIELDS.IS_PERSONAL]: true,
  [PersonalQueueModel.FIELDS.IS_PUBLIC]: false
});
```

## Repositories

Repositories handle direct Firebase operations.

### PublicQueueRepository

```javascript
const repo = window.publicQueueRepo;

// Save a queue
const queueId = await repo.save(publicQueue);

// Load a queue
const loadedQueue = await repo.load('queue123');

// List queues with sorting
const queues = await repo.list({
  sortBy: 'popular', // 'popular', 'recent', 'newest', 'longest'
  limit: 10
});

// Increment view count
await repo.incrementViewCount('queue123');

// Delete a queue
await repo.delete('queue123');
```

### PersonalQueueRepository

```javascript
const repo = window.personalQueueRepo;

// Save a personal queue
const queueId = await repo.save(personalQueue);

// Load a personal queue
const loadedQueue = await repo.load('personal_user123_1234567890');

// List queues for a user
const userQueues = await repo.listForUser('user123');

// Legacy: Load queue for user (single queue per user)
const userQueue = await repo.loadForUser('user123');
```

## Services

Services provide high-level business logic and are the recommended way to interact with queues.

### PublicQueueService

```javascript
const service = window.publicQueueService;

// Save current queue as public
const queueId = await service.saveCurrentQueue();

// Save with specific ID (update existing)
const queueId = await service.saveCurrentQueue('existing123');

// Load a public queue and set as current
const success = await service.loadQueue('queue123');

// List public queues
const result = await service.listQueues({
  sortBy: 'popular',
  limit: 20
});
```

### PersonalQueueService

```javascript
const service = window.personalQueueService;

// Save current queue as personal
const queueId = await service.saveCurrentQueue('user123', 'My Playlist');

// Load and play a personal queue
const success = await service.loadAndPlayQueue('personal_user123_1234567890');

// List personal queues for user
const queues = await service.listQueuesForUser('user123');
```

## Migration from Old Code

### Before (using string keys directly):

```javascript
// Old way - error-prone string keys
const docData = {
  id: queueId,
  queueData: queueData,
  metadata: {
    title: queueMetadata.title,
    videoCount: queueMetadata.videoCount,
    totalDuration: queueMetadata.totalDuration,
    // ... more fields
  }
};

await db.collection('queues').doc(queueId).set(docData);
```

### After (using models):

```javascript
// New way - type-safe with constants
const publicQueue = new PublicQueueModel({
  [PublicQueueModel.FIELDS.ID]: queueId,
  [PublicQueueModel.FIELDS.QUEUE_DATA]: queueData.toObject(),
  [PublicQueueModel.FIELDS.METADATA]: metadata.toObject()
});

await window.publicQueueRepo.save(publicQueue);

// Or even simpler with services:
await window.publicQueueService.saveCurrentQueue(queueId);
```

## Benefits

1. **Type Safety**: Field names are constants, preventing typos
2. **Intellisense**: Better IDE support with autocomplete
3. **Validation**: Models can validate data before saving
4. **Consistency**: Standardized way to handle Firebase data
5. **Maintainability**: Changes to field names only need to be made in one place
6. **Testing**: Easier to mock and test with structured models

## Field Constants Reference

### VideoModel.FIELDS
- `ID`: 'id'
- `TITLE`: 'title'
- `THUMBNAIL`: 'thumbnail'
- `DURATION`: 'duration'
- `CHANNEL`: 'channel'
- `DESCRIPTION`: 'description'
- `PUBLISHED_AT`: 'publishedAt'
- `VIEW_COUNT`: 'viewCount'

### QueueDataModel.FIELDS
- `QUEUE`: 'queue'
- `CURRENT_INDEX`: 'currentIndex'
- `IS_PLAYING`: 'isPlaying'
- `TIMESTAMP`: 'timestamp'
- `TITLE`: 'title'

### QueueMetadataModel.FIELDS
- `TITLE`: 'title'
- `VIDEO_COUNT`: 'videoCount'
- `TOTAL_DURATION`: 'totalDuration'
- `FIRST_VIDEO_THUMBNAIL`: 'firstVideoThumbnail'
- `CREATED_AT`: 'createdAt'
- `LAST_MODIFIED`: 'lastModified'
- `VIEW_COUNT`: 'viewCount'

### PublicQueueModel.FIELDS
- `ID`: 'id'
- `QUEUE_DATA`: 'queueData'
- `METADATA`: 'metadata'

### PersonalQueueModel.FIELDS
- `ID`: 'id'
- `USER_ID`: 'userId'
- `QUEUE_DATA`: 'queueData'
- `LAST_MODIFIED`: 'lastModified'
- `CREATED_AT`: 'createdAt'
- `IS_PERSONAL`: 'isPersonal'
- `IS_PUBLIC`: 'isPublic'

## Testing the Models

You can test the models in the browser console:

```javascript
// Test model validation
exampleModelValidation();

// Test custom queue operations
exampleCustomQueueOperation();

// Test personal queue operations
examplePersonalQueueOperation();

// Test advanced querying
exampleAdvancedQuerying();
```
